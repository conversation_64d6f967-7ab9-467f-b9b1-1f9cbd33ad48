import {
  forecastDemandRequest,
  forecastPlatformRequest,
} from "@/services/request";

export const getFiscalWeeks = () => {
  return forecastDemandRequest.request({
    method: "get",
    url: "/demand/fiscal_weeks ",
  });
};

export const getHomePage = (params) => {
  return forecastDemandRequest.request({
    method: "get",
    url: "/demand/homepage",
    params,
  });
};

export const getMenu = (params) => {
  return forecastDemandRequest.request({
    method: "get",
    url: "/demand/ideal_demand/menu_for_Y_X",
    params,
  });
};

// Y Value Setting
export const queryYSetting = (params) => {
  return forecastDemandRequest.request({
    method: "get",
    url: "/demand/ideal_demand/query_y_setting",
    params,
  });
};

export const updateYSetting = (data) => {
  return forecastDemandRequest.request({
    method: "post",
    url: "/demand/ideal_demand/update_y_setting",
    data,
  });
};

export const publishYSetting = (data) => {
  return forecastDemandRequest.request({
    method: "post",
    url: "/demand/ideal_demand/publish_y_setting",
    data,
  });
};

export const getMenuY = (params) => {
  return forecastDemandRequest.request({
    method: "get",
    url: "/demand/ideal_demand/menu_for_y",
    params,
  });
};
export const getMenuX = (params) => {
  return forecastDemandRequest.request({
    method: "get",
    url: "/demand/ideal_demand/menu_for_x",
    params,
  });
};

export const getQueryX = (params) => {
  return forecastDemandRequest.request({
    method: "get",
    url: "/demand/query_x",
    params,
  });
};

export const getRtmSalesForecast = (params) => {
  return forecastDemandRequest.request({
    method: "get",
    url: "/demand/rtm_sales_forecast",
    params,
  });
};

export const getQueryDfa = (params) => {
  return forecastDemandRequest.request({
    method: "get",
    url: "/demand/dfa/query",
    params,
  });
};

export const publishDfa = (data) => {
  return forecastDemandRequest.request({
    method: "post",
    url: "/demand/dfa/publish",
    data,
  });
};

export const getDfaTemplate = (params) => {
  return forecastDemandRequest.request({
    method: "get",
    url: "/demand/dfa/template",
    params,
    responseType: "blob",
  });
};

export const getDfaDownload = (params) => {
  return forecastDemandRequest.request({
    method: "get",
    url: "/demand/dfa/download",
    params,
    responseType: "blob",
  });
};

export const postDfaUpload = (params) => {
  return forecastDemandRequest.post(
    `/demand/dfa/upload?fiscal_week=${params.fiscal_week}`,
    params.formData
  );
};

export const getDfaDel = (params) => {
  return forecastDemandRequest.get("/demand/dfa/delete", {
    params,
  });
};

export const getRtmSalesForecastTemplate = (params) => {
  return forecastDemandRequest.request({
    method: "get",
    url: "/demand/rtm_sales_forecast/download_template",
    params,
    responseType: "blob",
  });
};
export const getRtmSalesForecastDownload = (params) => {
  return forecastDemandRequest.request({
    method: "get",
    url: "/demand/rtm_sales_forecast/download",
    params,
    responseType: "blob",
  });
};

export const postRtmSalesForecastUpload = (params) => {
  return forecastDemandRequest.post(
    `/demand/rtm_sales_forecast/upload?fiscal_week=${params.fiscal_week}&channel=${params.channel}`,
    params.formData
  );
};

export const getRtmSalesForecastDel = (params) => {
  return forecastDemandRequest.get("/demand/rtm_sales_forecast/delete", {
    params,
  });
};

export const updateXSetting = (data) => {
  return forecastDemandRequest.request({
    method: "post",
    url: "/demand/ideal_demand/update_x_setting",
    data,
  });
};

export const publishXandFcst = (data) => {
  return forecastDemandRequest.request({
    method: "post",
    url: "/demand/ideal_demand/publish_x_and_fcst",
    data,
  });
};

export const getRtmHomePage = (params) => {
  return forecastDemandRequest.request({
    method: "get",
    url: "/demand/rtm_homepage",
    params,
  });
};

export const resultDownload = ({ params, url }) => {
  return forecastDemandRequest.request({
    method: "get",
    url: url,
    params,
    responseType: "blob",
  });
};

export const rtmSalesForecastDownload = (params) => {
  return forecastDemandRequest.request({
    method: "get",
    url: "/demand/rtm_sales_forecast/download",
    params,
    responseType: "blob",
  });
};

export const getMenuWoi = (params) => {
  return forecastDemandRequest.request({
    method: "get",
    url: "/demand/sellin_demand/menu_for_woi",
    params,
  });
};

export const queryWoi = (params) => {
  return forecastDemandRequest.request({
    method: "get",
    url: "/demand/sellin_demand/query_woi_setting",
    params,
  });
};

export const updateWoi = (data) => {
  return forecastDemandRequest.request({
    method: "post",
    url: "/demand/sellin_demand/update_woi_setting",
    data,
  });
};
export const publishWoi = (data) => {
  return forecastDemandRequest.request({
    method: "post",
    url: "/demand/sellin_demand/publish_woi_setting",
    data,
  });
};

export const getCurrentWeek = (data) => {
  return forecastDemandRequest.request({
    method: "get",
    url: "/dashboard/current_week",
    data,
  });
};

//final demand
export const getDFOverview = (params) => {
  return forecastDemandRequest.request({
    method: "get",
    url: "/demand/final_demand/overview_sublobs",
    params,
  });
};

export const getDFPublish = (params) => {
  return forecastDemandRequest.request({
    method: "get",
    url: "/demand/final_demand/publish",
    params,
  });
};

export const getDFDownload = (params, cancelToken) => {
  return forecastDemandRequest.request({
    method: "get",
    url: "/demand/final_demand/download",
    params,
    responseType: "blob",
    cancelToken: cancelToken,
  });
};

export const getDFAdjustment = (params) => {
  return forecastDemandRequest.request({
    method: "get",
    url: "/demand/final_demand/detail",
    params,
  });
};

export const postDFUpload = (params) => {
  return forecastDemandRequest.post(
    `/demand/final_demand/upload?fiscal_week=${params.fiscal_week}`,
    params.formData
  );
};

export const saveDataConfig = (params) => {
  return forecastDemandRequest.post(
    `/demand/final_demand/save_data_config`,
    params
  );
};

export const getDataConfig = (params) => {
  return forecastDemandRequest.request({
    method: "get",
    url: "/demand/final_demand/get_data_config",
    params,
  });
};

//pos allocation ship-to文件下载相关接口
// export const AllocationDetailsShipTo = (params, cancelToken) => {
//   return forecastDemandRequest.request({
//     method: "get",
//     url: "/allocation/pos_allocation/files/npp_self_download",
//     params,
//     responseType: "blob",
//     cancelToken: cancelToken,
//   });
// };

export const cpfQuantileDownload = (params) => {
  return forecastDemandRequest.request({
    url: "/fast_end_to_end/ml_fcst_quantile/download",
    method: "get",
    params,
    responseType: "blob",
  });
};

export const quantileMenu = (params) => {
  return forecastDemandRequest.request({
    method: "get",
    url: "/fast_end_to_end/ml_fcst_quantile/menu",
    params,
  });
};

export const quantileList = (params) => {
  return forecastDemandRequest({
    method: "get",
    url: `/fast_end_to_end/ml_fcst_quantile/list`,
    params,
  });
};

export const quantilePublish = (params) => {
  return forecastDemandRequest.post(
    `/fast_end_to_end/ml_fcst_quantile/publish`,
    params
  );
};

export const quantileUpdate = (data) => {
  return forecastDemandRequest.request({
    method: "post",
    url: `/fast_end_to_end/ml_fcst_quantile/update`,
    data,
  });
};

export const quantileBatchUpdate = (params) => {
  return forecastDemandRequest.post(
    `/fast_end_to_end/ml_fcst_quantile/batch_update`,
    params
  );
};

export const feedbackSettingMenu = (params) => {
  return forecastDemandRequest.request({
    method: "get",
    url: "/fast_end_to_end/feedback_setting/menu",
    params,
  });
};

export const feedbackSettingList = (params) => {
  return forecastDemandRequest.post(
    `/fast_end_to_end/feedback_setting/list`,
    params
  );
};

export const feedbackSettingPublish = (params) => {
  return forecastDemandRequest.post(
    `/fast_end_to_end/feedback_setting/publish`,
    params
  );
};

export const feedbackSettingUpdate = (data) => {
  return forecastDemandRequest.request({
    method: "post",
    url: `/fast_end_to_end/feedback_setting/update`,
    data,
  });
};

export const feedbackSettingBatchUpdate = (params) => {
  return forecastDemandRequest.post(
    `/fast_end_to_end/feedback_setting/batch_update`,
    params
  );
};

export const cpfSearchPublish = (params) => {
  return forecastDemandRequest.post(
    "/fast_end_to_end/demand_overview/get_published_status",
    params
  );
};

export const cpfMenu = (params) => {
  return forecastDemandRequest.request({
    method: "get",
    url: "/fast_end_to_end/demand_overview/menu",
    params,
  });
};

export const cpfDemandList = (params, path, cancelToken) => {
  if (path === "channel_view") {
    return forecastDemandRequest.request({
      method: "get",
      url: `/fast_end_to_end/demand_overview/${path}`,
      params,
      cancelToken: cancelToken,
    });
  }
  return forecastDemandRequest.post(
    `/fast_end_to_end/demand_overview/${path}`,
    params,
    { cancelToken: cancelToken }
  );
};

export const cpfChannelDownload = (params) => {
  return forecastDemandRequest.request({
    url: "/fast_end_to_end/demand_overview/download",
    method: "get",
    params,
    responseType: "blob",
  });
};

export const rtmFeedbackMenu = (params) => {
  return forecastPlatformRequest.request({
    method: "get",
    url: "/feedback/menu",
    params,
  });
};

export const rtmFeedbackTotal = (params) => {
  return forecastPlatformRequest.request({
    method: "get",
    url: "/feedback/count",
    params,
  });
};

export const rtmFeedbackList = (params) => {
  return forecastPlatformRequest.request({
    method: "get",
    url: "/feedback/list",
    params,
  });
};

export const rtmFeedbackPublish = (params) => {
  return forecastPlatformRequest.post("/feedback/third/review", params);
};

export const approvalStatus = (params) => {
  return forecastPlatformRequest.request({
    method: "get",
    url: "/feedback/review/status",
    params,
  });
};

export const batchUpload = (params) => {
  return forecastDemandRequest.post(
    `/demand/final_demand/upload_v3?fiscal_week=${params.fiscal_week}`,
    params.formData
  );
};

export const quantileNewMenu = (params) => {
  return forecastDemandRequest.get(
    "/fast_end_to_end/ml_fcst_quantile_recommendation/menu",
    { params }
  );
};

export const quantileNewList = (params) => {
  return forecastDemandRequest.get(
    "/fast_end_to_end/ml_fcst_quantile_recommendation/list",
    { params }
  );
};
