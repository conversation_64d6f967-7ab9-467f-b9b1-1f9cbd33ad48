<template>
  <div
    v-load="{
      show: loading,
      text: 'Loading..',
    }"
    class="table-wrap"
    v-resize.immediate="{
      target: bodyDom,
      event: setTableHeight,
    }"
  >
    <VirtualScroll
      ref="virtual"
      :data="tableData"
      :dynamic="true"
      :buffer="600"
      key-prop="unique_key"
      v-tool-tip.delegate="{ targetClassName: 'toolBox' }"
      @change="(virtualList) => (virtualData = virtualList)"
    >
      <el-table
        ref="product-table"
        :data="virtualData"
        :max-height="tableHeight"
        row-key="unique_key"
        :border="true"
        :class="{ fixEleFixed: showEnd }"
        :cellClassName="cellClassName"
      >
        <el-table-column
          v-for="column in columns"
          :key="column.prop"
          v-bind="column"
          :width="column?.width * zoomValue"
        >
          <template slot-scope="scope">
            <template v-if="column.definedSlot === 'soldTo'">
              <div class="toolBox" :class="{ ellipsis: !otherValue.range }">
                {{ scope.row[column.prop] }}
              </div>
            </template>
            <template v-else-if="column.definedSlot === 'value'">
              <div class="value-cell">
                <p
                  class="forecast_value"
                  :class="
                    valueClass(scope.row[`uncertainty_level_${column.prop}`])
                  "
                >
                  {{ fcst_actVal(scope.row, column.prop) }}
                </p>
                <p class="min-max" v-show="otherValue.range">
                  {{ rangeVal(scope.row, column.prop) }}
                </p>
                <p class="min-max" v-show="line3Toggle(scope.row)">
                  {{
                    rangeVal(scope.row, column.prop) === "-"
                      ? "-"
                      : scope.row?.[`quantile_level_${column.prop}`]
                  }}
                </p>
              </div>
            </template>

            <template v-else>
              {{ scope.row[column.prop] ? scope.row[column.prop] : "-" }}
            </template>
          </template>
        </el-table-column>
        <template #append v-if="tableData.length">
          <div class="append" ref="append">
            <div class="end" v-if="showEnd">— — End of the line. — —</div>
            <div class="load-more" v-else v-load.inline="true">Loading</div>
          </div>
        </template>
        <template #empty>
          <EmptyView text="No Data" v-show="!loading" />
        </template>
      </el-table>
    </VirtualScroll>
    <transition name="el-fade-in-linear">
      <div
        class="show-more"
        v-show="!hideMore && tableData.length && !isScrolling"
      >
        <ShowMore />
      </div>
    </transition>
    <div class="back-top" v-show="showBackTop" @click="handleBack2Top">
      <svg-icon data_iconName="appeal-back2top" />
    </div>
  </div>
</template>
<script>
import { VirtualScroll } from "@/components/VirtualScroll";
import EmptyView from "@/pages/ChannelCompliance/components/EmptyView.vue";
import ShowMore from "@/pages/AIProgram/components/ShowMore.vue";

import { isNullOrUndefinedOrEmptyString } from "../format";
export default {
  components: {
    VirtualScroll,
    EmptyView,
    ShowMore,
  },
  props: {
    columns: {
      type: Array,
      default: () => [],
    },
    tableData: {
      type: Array,
      default: () => [],
    },
    total: {
      type: Number,
      default: 0,
    },
    otherValue: {
      type: Object,
      default: () => {},
    },
    filterValue: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      virtualData: [],
      hideMore: false,
      isScrolling: false,
      showBackTop: false,
      scrollDom: null,
      tableHeight: 368,
      ob: null,
      loading: false,

      scrollTimer: null,
      bodyDom: document.documentElement,
    };
  },
  computed: {
    zoomValue() {
      return this.$store.state.newNavZoomValue;
    },
    showEnd() {
      return this.tableData.length && this.tableData.length === this.total;
    },
  },
  watch: {
    tableData: {
      handler(v) {
        if (v.length) {
          if (!this.ob) {
            this.$nextTick(() => {
              this.initObserver();
            });
          }
        } else {
          this.ob?.disconnect();
          this.ob = null;
          this.virtualData = [];
          this.$nextTick(() => {
            this.$refs.virtual.renderAllData();
          });
        }
        this.$nextTick(() => {
          this.handleScroll({ target: this.scrollDom });
        });
      },
      deep: true,
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.$refs.virtual?.slowOnMousewheel(2);
      this.scrollDom = this.$el.querySelector(".el-table__body-wrapper");
      console.log("this.scrollDom", this.scrollDom);
      if (this.scrollDom)
        this.scrollDom.addEventListener("scroll", this.handleScroll);
    });
  },
  methods: {
    // 设置observe，监听append插槽，请求更多数据
    initObserver() {
      this.ob = new IntersectionObserver((entries) => {
        this.hideMore = entries[0].isIntersecting;
        console.log("this.hideMore", this.hideMore);
        if (entries[0].isIntersecting && this.tableData.length < this.total) {
          this.$emit("more");
        }
      });
      this.ob.observe(this.$refs?.append);
    },
    handleScroll(e) {
      const { scrollHeight = 0, offsetHeight = 0, scrollTop = 0 } = e?.target;
      this.showBackTop = scrollTop >= 2 && scrollHeight > offsetHeight;

      this.isScrolling = true;
      clearTimeout(this.scrollTimer);
      this.scrollTimer = setTimeout(() => {
        this.isScrolling = false;
      }, 300);
    },

    handleBack2Top() {
      if (this.scrollDom) {
        this.scrollDom.scrollTo({ top: 0, behavior: "smooth" });
      }
      // this.$refs?.virtual?.scrollTo(0); // 备选方案
    },
    setTableHeight() {
      const newHeight = document.body.clientHeight - 284 * this.zoomValue;
      console.log(document.body.clientHeight, newHeight);
      this.tableHeight = newHeight < 368 ? 368 : newHeight;
    },
    valueClass(level) {
      if (this.otherValue.fcst_act === "Forecast") {
        return level;
      }
    },
    fcst_actVal(item, prop) {
      if (isNullOrUndefinedOrEmptyString(item)) return "-";

      const valueType =
        this.otherValue.fcst_act === "Forecast"
          ? "forecast_value"
          : "actual_ub_value";
      const value = item[`${valueType}_${prop}`];

      return isNullOrUndefinedOrEmptyString(value) ? "-" : value;
    },
    rangeVal(item, prop) {
      if (isNullOrUndefinedOrEmptyString(item)) return "-";
      let minValue = item?.[`forecast_min_${prop}`];
      if (isNullOrUndefinedOrEmptyString(minValue)) {
        minValue = "-";
      }
      let maxValue = item?.[`forecast_max_${prop}`];
      if (isNullOrUndefinedOrEmptyString(maxValue)) {
        maxValue = "-";
      }
      if (minValue === "-" && maxValue === "-") return "-";
      return `${minValue} ~ ${maxValue}`;
    },
    line3Toggle(row) {
      if (!this.otherValue.range) return false;
      if (this.filterValue.sub_lob === "All") {
        return false;
      }
      if (row.sub_rtm === "All") {
        return false;
      }
      return true;
    },
    cellClassName({ row, column }) {
      if (this.otherValue.range && this.otherValue.fcst_act === "Actual") {
        if (row?.[`rangeBg_${column.property}`]) {
          return "rangeBg";
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.value-cell {
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
  color: #1c1c1e;

  .min-max {
    color: #6e6e73;
    font-weight: 300;
  }
}
.High {
  color: #f63f54;
}
.Mid {
  color: #ffae3d;
}
.Low {
  color: #34c759;
}
::v-deep .el-table {
  border-radius: 16px;
  &.el-table--border {
    border: none;
    .el-table__row {
      .el-table__cell {
        &:last-child {
          border-right: none;
        }
      }
    }
    &::after {
      display: none;
    }
  }
  &::before,
  .el-table__fixed::before {
    display: none;
  }
  .el-table__header {
    .el-table__cell {
      padding: 0;
      height: 40px;
      border-bottom: 1px solid #e5e5ea;
      box-sizing: border-box;
      background: #fafafa;
      .cell {
        @include font(14px, 20px, #6e6e73);
        font-weight: 500;
        padding: 0 0 0 16px;
        font-weight: normal;
      }
    }
  }
  .el-table__row {
    .el-table__cell {
      padding: 0;
      vertical-align: top;
      &.rangeBg {
        background-color: rgba(0, 113, 227, 0.08) !important;
      }
    }
    .cell {
      padding: 0;
      word-break: break-word;
      word-wrap: break-word;
    }
  }
  .el-table__empty-block {
    border-radius: 16px;
    min-height: 300px;
    border-bottom: 1px solid #e5e5ea;
    box-sizing: border-box;
    .el-table__empty-text {
      @include flex(center);
    }
  }
  .el-table__body {
    tr:hover > td.el-table__cell {
      background-color: transparent;
    }
    tr.hover-row > td.el-table__cell {
      background-color: transparent;
    }
  }
}

.fixEleFixed {
  ::v-deep
    .el-table__fixed
    .el-table__fixed-body-wrapper
    .el-table__append-gutter {
    height: 66px !important;
  }
}

.append {
  @include flex(center);
  @include font(14px, 20px, #d1d1d6);
  padding: 14px 0;
  position: relative;
  z-index: 99;
  &:has(> .load-more) {
    padding-top: 14px;
  }
  &:has(> .end) {
    padding-top: 32px;
  }

  .load-more {
    @include flex(center);
    gap: 4px;
    color: #3a3a3c;
    ::v-deep .expert-directive__loading {
      font-size: 16px;
      order: -1;
      color: #1c1c1e;
    }
  }

  .end {
    @include flex();
    flex-direction: column;
    gap: 12px;
  }
}
.table-wrap {
  position: relative;
  border-radius: 16px;
  border: 1px solid #e5e5ea;
  // &:has(.el-table__empty-block) {
  //   border-bottom: 1px solid #e5e5ea;
  // }

  .show-more {
    position: absolute;
    bottom: 8px;
    left: 50%;
    z-index: 99;
    transform: translateX(-50%);
    @include flex(center);
  }

  &::after {
    content: "";
    display: block;
    position: absolute;
    bottom: 0px;
    left: 0px;
    right: 0;
    height: 56px;
    width: 100%;
    border-bottom-right-radius: 16px;
    border-bottom-left-radius: 16px;
    background: linear-gradient(
      to bottom,
      rgba(255, 255, 255, 0),
      rgba(255, 255, 255, 1) 35%
    );
    z-index: 9;
  }

  .back-top {
    position: absolute;
    right: 0;
    bottom: 4px;
    width: 40px;
    height: 40px;
    @include flex(center);
    border-radius: 50%;
    background: rgba(#3a3a3c, 0.4);
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s linear;
    z-index: 101;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    .svg-icon {
      font-size: 40px;
      color: #fff;
    }
    &:hover {
      background: rgba(#3a3a3c, 0.7);
    }
  }
  .toolBox {
    cursor: default;
    position: relative;
    word-break: break-all;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2; /* 这里是超出几行省略 */
    line-clamp: 2;
    overflow: hidden;
    word-break: break-word;
  }
  .ellipsis {
    -webkit-line-clamp: 1;
    line-clamp: 1;
    word-break: break-all;
  }
}
::v-deep .expert-directive__loading {
  font-size: 32px;
  color: #aeaeb2;
  z-index: 999999999 !important;

  flex-direction: column;
  span {
    font-size: 12px;
  }
}
</style>
