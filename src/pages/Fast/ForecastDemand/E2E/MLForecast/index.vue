<template>
  <div
    class="view"
    v-log.visible="{
      event_name: 'fast_e2e_cpf_view_ml_fcst_quantile_page_general_expose',
    }"
    v-log:67.stay="{
      event_name:
        'fast_e2e_cpf_view_ml_fcst_quantile_page_general_stay_duration',
    }"
  >
    <Title
      title="ML Quantile Forecast"
      backTitle="Forecast & Demand"
      :isSecond="true"
      :note="true"
      @back="handleBack"
    >
      <template slot="right">
        <div class="weeks">
          <svg-icon data_iconName="icon_date" />
          Fiscal Week：{{ fiscal_week_name }}
        </div>
      </template>
    </Title>

    <component
      :is="renderComponent"
      :currentLob="currentLob"
      :fiscal_week_name="fiscal_week_name"
      :isOldView="isOldView"
      :disabled="disabled"
      :params="params"
    />
  </div>
</template>

<script>
// import { fiscalWeek2Number } from "@/utils/dom";
import { checkStepStatus, getCurrentWeek } from "@/services/fastDashboard";
import oldIndex from "./oldIndex.vue";
import newIndex from "./newIndex.vue";
import Title from "@/pages/Fast/components/Title.vue";

export default {
  name: "ML_Quantile_Forecast",
  components: {
    oldIndex,
    newIndex,
    Title,
  },
  data() {
    return {
      disabled: true,
    };
  },
  computed: {
    fiscal_week_name() {
      return this.$route.query.fiscalWeek;
    },
    currentLob() {
      return this.$route.query.lob;
    },
    isOldView() {
      return false; //fiscalWeek2Number(this.fiscal_week_name) < 25409; TODO
    },
    renderComponent() {
      return this.isOldView ? oldIndex : newIndex;
    },
    params() {
      return {
        fiscal_week_name: this.fiscal_week_name,
        lob: this.currentLob,
      };
    },
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      Promise.all([checkStepStatus(["FAST_E2E_QUANTILE"]), getCurrentWeek()])
        .then(([res1, res2]) => {
          if (res1?.code === 0 && res2?.code === 0) {
            this.disabled = false;
            //!res1?.data?.[0]?.is_on || this.fiscal_week_name !== res2?.data; TODO
          }
        })
        .catch((error) => {
          console.warn(error);
        });
    },

    handleBack() {
      const { type } = this.$route.query;
      if (type) {
        this.$router.replace({
          path: "/fd-e2e/cpf",
        });
      } else {
        this.$router.go(-1);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.view {
  padding: 40px;

  .weeks {
    color: #aeaeb2;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
  }

  ::v-deep .filterWrapper {
    @include flex();
    padding: 20px 0;
    border-bottom: 1px solid #e5e5ea;
  }

  ::v-deep .btn .expert-directive__loading {
    font-size: 16px;
    order: -1;
  }
}
</style>
