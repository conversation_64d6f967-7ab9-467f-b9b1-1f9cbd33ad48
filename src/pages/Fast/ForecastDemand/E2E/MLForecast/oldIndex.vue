<template>
  <div>
    <HeaderView
      :currentLob="currentLob"
      :lastPublishTime="lastPublishTime"
      :isEdit="isEdit"
      :publishing="publishing"
      :disabled="disabled"
      @publish="publishService"
    />
    <Card title="ML Quantile Forecast Setting" class="data-container">
      <template #options>
        <TitleOption :params="params" :fiscal_week_name="fiscal_week_name" />
      </template>
      <template>
        <div class="filterWrapper">
          <CustomSelect
            size="small"
            v-model="subLob"
            :disabled="isEdit"
            :options="subLobOptions"
            @change="filterQuantileList"
          >
            <template slot="prefix">Sub-LOB: </template>
          </CustomSelect>
          <BtnView
            :isEdit="isEdit"
            :disabled="disabled"
            @reset="getQuantileList"
            @save="handleSave"
            @cancel="handleCancel"
            @edit="isEdit = true"
            @batch="showBatch = true"
          />
        </div>
        <TableView
          :tableColumn="tableColumn"
          :tableData="tableData"
          :loading="loading"
          :isEdit="isEdit"
          @levelChange="levelChange"
        />
      </template>
    </Card>
    <BatchDrawer
      v-if="showBatch"
      :visible.sync="showBatch"
      :fiscal-week-list="weekList"
      :rtm-list="rtmList"
      :sub-rtm-list="subRtmList"
      :sub-lob-list="subLobOptions"
      :level-list="levelOption"
      @closeDrawer="handleBatch"
    />
  </div>
</template>
<script>
import CustomSelect from "@/components/CustomSelect";
import Card from "@/pages/Fast/components/Card.vue";
import {
  quantileMenu,
  quantileList,
  quantilePublish,
  quantileUpdate,
  quantileBatchUpdate,
} from "@/services/ForecastDemand.js";
import BatchDrawer from "./components/BatchDrawer.vue";
import { formatSelect } from "@/pages/Fast/ForecastDemand/formatData";
import HeaderView from "./components/HeaderView.vue";
import BtnView from "./components/BtnView.vue";
import TableView from "./components/TableView.vue";
import TitleOption from "./components/TitleOption.vue";
import {
  formatTableData,
  formatValue,
  restoreTableData,
  formatColumn,
  formatList,
} from "./format";

export default {
  components: {
    Card,
    BatchDrawer,
    CustomSelect,
    HeaderView,
    BtnView,
    TableView,
    TitleOption,
  },
  props: {
    currentLob: {
      type: String,
      default: "",
    },
    fiscal_week_name: {
      type: String,
      default: "",
    },
    disabled: {
      type: Boolean,
      default: true,
    },
    params: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      loading: false,
      isEdit: false,
      publishing: false,
      showBatch: false,
      pageLoading: null,

      tableColumn: [],
      oTableData: null,
      tableData: [],

      fiscal_week: "",
      forecast_weeks: [],
      lastPublishTime: "",

      subLob: "",
      subLobOptions: [],
      rtmList: [],
      subRtmList: [],
      levelOption: [],
    };
  },
  computed: {
    weekList() {
      return this.forecast_weeks.map((it, index) => ({
        label: `CW${index > 0 ? "+" + index : ""}(${it.substr(4)})`,
        value: it,
      }));
    },
  },
  created() {
    this.pageLoading = this.$fastloading();
    this.getQuantileMenu();
  },
  methods: {
    formatValue,
    getQuantileMenu(noChangeSubLob) {
      quantileMenu(this.params)
        .then(({ code = 0, data = {} }) => {
          if (code === 0) {
            this.fiscal_week = data?.fiscal_week;
            this.forecast_weeks = data?.forecast_weeks;
            this.lastPublishTime = data?.latest_publish_time;
            this.subLobOptions = formatSelect(data?.sublobs ?? []);
            if (!noChangeSubLob) {
              this.subLob = this.subLobOptions?.[0]?.value || ""; //"iPhone 16 Pro Max";
            }
            this.rtmList = data?.rtms?.map((it) => ({
              ...it,
              value: it.rtm,
              label: it.rtm,
            }));
            this.subRtmList = data?.rtms?.reduce((pre, item) => {
              pre.push(...formatSelect(item.sub_rtms));
              return pre;
            }, []);
            this.tableColumn = formatColumn(data?.forecast_weeks ?? []);
            this.getQuantileList();
          }
        })
        .catch((error) => {
          console.warn(error);
        });
    },
    async getQuantileList() {
      this.loading = true;
      try {
        const { code = 0, data = {} } = await quantileList(this.params);
        if (code === 0) {
          const { data: list, levelOption } = formatList(data);
          this.levelOption = levelOption;
          this.oTableData = list;
          this.tableData = formatTableData(
            this.oTableData?.[this.subLob] ?? [],
            this.forecast_weeks,
            this.subLob
          );
        }
      } catch (error) {
        console.warn(error);
      } finally {
        this.loading = false;
        this.pageLoading?.close();
      }
    },
    levelChange(row) {
      this.tableData.splice(0, 1, row);
    },
    filterQuantileList(val) {
      this.loading = true;
      this.tableData = formatTableData(
        this.oTableData?.[val] ?? [],
        this.forecast_weeks,
        this.subLob
      );
      this.loading = false;
    },
    publishService() {
      this.publishing = true;
      this.loading = true;
      quantilePublish({
        ...this.params,
        fiscal_week: this.fiscal_week,
        forecast_weeks: this.forecast_weeks,
        data: this.oTableData,
      })
        .then(({ code = 0 }) => {
          if (code === 0) {
            this.$message.success({
              message:
                "Result Published, You can update quantile range again before 12:00.",
              duration: 5000,
            });
            this.getQuantileMenu(true);
          }
        })
        .catch((error) => {
          console.warn(error);
        })
        .finally(() => {
          this.loading = false;
          this.publishing = false;
        });
    },
    async handleCancel() {
      await this.getQuantileList();
      this.isEdit = false;
    },
    handleSave() {
      this.saveLoading = this.$fastloading({ text: "Saving" });
      quantileUpdate({
        ...this.params,
        fiscal_week: this.fiscal_week,
        forecast_weeks: this.forecast_weeks,
        data: restoreTableData(this.oTableData, this.tableData, this.subLob),
      })
        .then(({ code = 0 }) => {
          if (code === 0) {
            this.isEdit = false;
            this.$message.success({
              message: "Result Saved.",
              duration: 5000,
            });
            this.getQuantileList();
          }
        })
        .catch((error) => {
          console.warn(error);
        })
        .finally(() => {
          this.saveLoading.close();
        });
    },
    handleBatch(params) {
      this.showBatch = false;
      this.saveLoading = this.$fastloading({ text: "Saving" });
      quantileBatchUpdate({
        fiscal_week: this.fiscal_week,
        forecast_weeks: this.forecast_weeks,
        ...this.params,
        ...params,
      })
        .then(({ code = 0 }) => {
          if (code === 0) {
            this.$message.success({
              message: "Result Saved.",
              duration: 5000,
            });
            this.getQuantileList();
          }
        })
        .catch((error) => {
          console.warn(error);
        })
        .finally(() => {
          this.saveLoading.close();
        });
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .card.data-container .content {
  display: block !important;
}
</style>
