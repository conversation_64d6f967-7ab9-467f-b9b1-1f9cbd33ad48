<template>
  <div class="title-option">
    <OperBtn
      v-log.click="{
        event_name: 'fast_ml_quantile_forecast_ml_forecast_certainty_report',
      }"
      class="edit-btn"
      @click="handleToForecast"
    >
      ML Forecast Certainty Report<svg-icon data_iconName="fast-arrow" />
    </OperBtn>
    <OperBtn
      v-log.click="{
        event_name: 'fast_e2e_cpf_view_ml_fcst_quantile_page_download_click',
      }"
      v-load.inline="downloading"
      :disabled="downloading"
      @click="handleDownload"
    >
      <svg-icon data_iconName="fast-download" v-if="!downloading" />Download
    </OperBtn>
  </div>
</template>

<script>
import OperBtn from "@/pages/Fast/NPIAllocation/NPISupplyAllocation/components/OperBtn.vue";
import { cpfQuantileDownload } from "@/services/ForecastDemand.js";
import { downloadFile } from "../format";
export default {
  components: {
    OperBtn,
  },
  props: {
    params: {
      type: Object,
      default: () => {},
    },
    fiscal_week_name: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      downloading: false,
    };
  },
  methods: {
    handleDownload() {
      this.downloading = true;
      cpfQuantileDownload({
        rtm: "CP&F",
        ...this.params,
      })
        .then((res) => {
          downloadFile(res, this.fiscal_week_name);
        })
        .catch((error) => {
          console.warn(error);
        })
        .finally(() => {
          this.downloading = false;
        });
    },

    handleToForecast() {
      this.$router.push({
        path: "/forecast-platform/certainty",
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.title-option {
  @include flex();
}
</style>
