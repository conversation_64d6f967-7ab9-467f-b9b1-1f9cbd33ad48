<template>
  <div class="operateBox">
    <OperBtn v-show="isEdit" class="reset-btn" @click="handleReset">
      <svg-icon data_iconName="fast-icon_reset_default" />Reset
    </OperBtn>

    <OperBtn v-show="isEdit" class="save-btn" @click="$emit('save')">
      <svg-icon data_iconName="icon_save_default" />Save
    </OperBtn>

    <OperBtn v-show="isEdit" class="save-btn" @click="$emit('cancel')">
      <svg-icon data_iconName="close-blue" />Cancel
    </OperBtn>

    <OperBtn
      v-show="!isEdit"
      class="edit-btn"
      @click="$emit('batch')"
      :disabled="disabled"
    >
      <svg-icon data_iconName="fast-batch_update" />Batch Update
    </OperBtn>

    <OperBtn
      v-show="!isEdit"
      class="edit-btn"
      @click="$emit('edit')"
      :disabled="disabled"
    >
      <svg-icon data_iconName="fast-icon_edit_default" />Edit
    </OperBtn>
  </div>
</template>

<script>
import OperBtn from "@/pages/Fast/NPIAllocation/NPISupplyAllocation/components/OperBtn.vue";

export default {
  components: {
    OperBtn,
  },
  props: {
    isEdit: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    handleReset() {
      this.$message.warning({
        fullscreen: true,
        showConfirmBtn: true,
        showCancelBtn: true,
        message:
          "The filled-in content will be reverted to the previous version.",
        onConfirm: async (close) => {
          close();
          this.$emit("reset");
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.operateBox {
  display: flex;
  align-items: center;

  .line {
    width: 1px;
    height: 20px;
    background-color: #e5e5ea;
    margin: 0 4px;
  }
}
</style>
