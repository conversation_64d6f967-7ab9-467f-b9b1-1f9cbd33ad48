<template>
  <div class="filters">
    <CustomSelect
      size="small"
      :value="selfSubLob"
      :disabled="isEdit"
      :options="subLobOptions"
      @change="handleSubLobChange"
    >
      <template slot="prefix">Sub-LOB: </template>
    </CustomSelect>
    <CustomSelect
      size="small"
      :value="selfNand"
      :disabled="isEdit"
      :options="nandOptions"
      @change="handleNandChange"
    >
      <template slot="prefix">Nand: </template>
    </CustomSelect>
    <CustomSelect
      size="small"
      v-model="selfColor"
      :disabled="isEdit"
      :options="colorOptions"
    >
      <template slot="prefix">Color: </template>
    </CustomSelect>
    <SearchInput
      :disabled="isEdit"
      :options="soldToOptions"
      :searchValue="selfSearchValue"
      @change:searchValue="changeSearchValue"
      placeholder="Search Sold-to Name / ID"
      placement="bottom"
      sold_to_name_key="short_name"
      class="search"
    />
  </div>
</template>

<script>
import CustomSelect from "@/components/CustomSelect";
import SearchInput from "@/pages/Fast/NPIAllocation/NPISupplyAllocation/components/SearchInput.vue";

export default {
  components: {
    CustomSelect,
    SearchInput,
  },
  props: {
    subLob: {
      type: String,
      default: "",
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    subLobOptions: {
      type: Array,
      default: () => [],
    },
    soldToOptions: {
      type: Array,
      default: () => [],
    },
    nand: {
      type: String,
      default: "",
    },
    color: {
      type: String,
      default: "",
    },
    searchValue: {
      type: String,
      default: "",
    },
  },
  data() {
    return {};
  },
  computed: {
    selfSubLob: {
      get() {
        return this.subLob;
      },
      set(v) {
        this.$emit("update:subLob", v);
      },
    },
    selfNand: {
      get() {
        return this.nand;
      },
      set(v) {
        this.$emit("update:nand", v);
      },
    },
    selfColor: {
      get() {
        return this.color;
      },
      set(v) {
        this.$emit("update:color", v);
      },
    },
    selfSearchValue: {
      get() {
        return this.searchValue;
      },
      set(v) {
        this.$emit("update:searchValue", v);
      },
    },
    nandOptions() {
      return (
        this.subLobOptions.find((it) => it.value === this.subLob)?.nand ?? []
      );
    },
    colorOptions() {
      return (
        this.subLobOptions.find((it) => it.value === this.subLob)?.color ?? []
      );
    },
  },
  methods: {
    handleSubLobChange(val) {
      this.selfSubLob = val;
      this.selfNand = "All";
      this.selfColor = "All";
    },

    handleNandChange(val) {
      this.selfNand = val;
      this.selfColor = "All";
    },

    changeSearchValue(value) {
      if (value) {
        this.selfSearchValue = value;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.filters {
  display: grid;
  grid-template-columns: 256px 200px 200px 256px;
  gap: 16px;

  .search {
    ::v-deep .name,
    ::v-deep .rtm {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
</style>
