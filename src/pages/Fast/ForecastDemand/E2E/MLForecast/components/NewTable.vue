<template>
  <div class="new-table">
    <Table
      class="e2eTable"
      ref="data"
      :columns="tableColumn"
      :data="tableData"
      :loading="loading"
      :elTableProps="{
        'row-key': 'id',
        'expand-row-keys': expandRowKeys,
      }"
      v-tool-tip.delegate="{ targetClassName: 'cell' }"
    >
      <template #headerMl="{ scope }">
        <template>
          {{ scope.column.label?.split("_")[0] }}
          <div style="color: #aeaeb2">
            {{ ` (${scope.column.label?.split("_")[1]})` }}
          </div>
        </template>
      </template>
      <template #headerRange="{ scope }">
        <template>
          {{ scope.column.label }}
          <div style="color: #aeaeb2">(Variance)</div>
        </template>
      </template>
      <template #rtm="{ row, item }">
        {{ row.hideRtm ? "" : row[item.prop] }}
      </template>
      <template #value="{ row, item }">
        {{ formatValue(row[item.prop]) }}
      </template>
      <template #quantileLevel="{ row, item, index }">
        <span v-show="!isEdit">
          {{ getNewQuantileListLabel(row, item, index) }}
        </span>
        <CustomSelect
          v-show="isEdit && index !== 0"
          size="small"
          v-model="row[item.prop]"
          :options="row[item.fiscalWeek + '+quantile_config']"
          :appendToBody="true"
          popper-class="appendToBody"
          @change="(val) => levelChange(val, row, item)"
        />
      </template>
      <template #range="{ row, item }">
        <p v-html="row[item.prop]"></p>
      </template>
    </Table>
  </div>
</template>

<script>
import Table from "@/pages/Fast/components/table/index.vue";
import CustomSelect from "@/components/CustomSelect";
import { getNewQuantileListLabel, formatValue } from "../format";
import { cloneDeep } from "lodash-es";
import BigNumber from "bignumber.js";

export default {
  components: {
    Table,
    CustomSelect,
  },
  props: {
    tableColumn: {
      type: Array,
      default: () => [],
    },
    tableData: {
      type: Array,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    expandRowKeys() {
      return this.tableData.map((it) => it?.id);
    },
  },
  methods: {
    getNewQuantileListLabel,
    formatValue,
    levelChange(val, row, item) {
      // let current =
      //   row[`${item.fiscalWeek}+quantile_config`].find(
      //     (it) => it.quantile_level === val
      //   ) || {};
      // row[`${item.fiscalWeek}+selected_quantile_level`] = val;
      // row[`${item.fiscalWeek}+range`] = `${
      //   formatValue(current?.min) || "-"
      // } <span>(${current?.minPercent}%)</span> ~ ${
      //   formatValue(current?.max) || "-"
      // } <span>(+${current.maxPercent}%)</span>`;
      // let arr = cloneDeep(this.tableData);
      // let quantileNumArr = this.forecast_weeks.map((it) => {
      //   return {
      //     fiscal_week_name: it,
      //     [`${it}+ml_forecast`]: 0,
      //     [`${it}+minRangeNum`]: 0,
      //     [`${it}+maxRangeNum`]: 0,
      //   };
      // });
      // arr.slice(1).forEach((it) => {
      //   it.forecast_quantile.forEach((item) => {
      //     let current = quantileNumArr.find(
      //       (it) => it.fiscal_week_name === item.fiscal_week_name
      //     );
      //     let current_quantile_config =
      //       it[item.fiscal_week_name + "+quantile_config"];
      //     current[`${item.fiscal_week_name}+ml_forecast`] += item.ml_forecast;
      //     current[`${item.fiscal_week_name}+minRangeNum`] +=
      //       current_quantile_config?.find(
      //         (i) =>
      //           i.quantile_level ===
      //           it[item.fiscal_week_name + "+selected_quantile_level"]
      //       )?.min;
      //     current[`${item.fiscal_week_name}+maxRangeNum`] +=
      //       current_quantile_config?.find(
      //         (i) =>
      //           i.quantile_level ===
      //           it[item.fiscal_week_name + "+selected_quantile_level"]
      //       )?.max;
      //   });
      // });
      // quantileNumArr.forEach((it) => {
      //   let ml_forecast = it[`${it.fiscal_week_name}+ml_forecast`] ?? 0;
      //   arr[0][`${it.fiscal_week_name}+ml_forecast`] = ml_forecast;
      //   if (ml_forecast === 0) {
      //     it[`${it.fiscal_week_name}+range`] =
      //       "-<span>(-%)</span> ~ -<span>(-%)</span>";
      //   } else {
      //     arr[0][`${it.fiscal_week_name}+range`] = `${formatValue(
      //       it[`${it.fiscal_week_name}+minRangeNum`]
      //     )} <span>(${BigNumber(it[`${it.fiscal_week_name}+minRangeNum`] ?? 0)
      //       .minus(BigNumber(ml_forecast ?? 0))
      //       .div(BigNumber(ml_forecast ?? 0))
      //       .times(100)
      //       .toFixed(0)}%)</span> ~ ${formatValue(
      //       it[`${it.fiscal_week_name}+maxRangeNum`]
      //     )} <span>(+${BigNumber(it[`${it.fiscal_week_name}+maxRangeNum`] ?? 0)
      //       .minus(BigNumber(ml_forecast ?? 0))
      //       .div(BigNumber(ml_forecast ?? 0))
      //       .times(100)
      //       .toFixed(0)}%)</span>`;
      //   }
      // });
      // this.$emit("levelChange", arr[0]);
    },
  },
};
</script>

<style lang="scss" scoped>
.new-table {
  ::v-deep .el-table {
    &::before {
      display: none;
    }
    .cell {
      overflow: visible;
      p span {
        color: #aeaeb2;
      }
    }
    .el-table__cell.col-border {
      border-right: 1px solid #e5e5ea;
    }
    .el-table__header tr {
      th.el-table__cell {
        height: 80px;
        border-color: #e5e5ea;
      }
    }
    .el-table__fixed-body-wrapper,
    .el-table__body-wrapper {
      .el-table__body {
        .el-table__cell {
          border-color: #e5e5ea;
          .cell {
            color: #1c1c1e !important;
            font-weight: 400;

            .el-table__expand-icon {
              background: url(@/assets/svg/common/table/expand.svg) no-repeat
                center;
              .el-icon-arrow-right::before {
                display: none;
              }
              &.el-table__expand-icon--expanded {
                transform: none !important;
                background: url(@/assets/svg/common/table/collapse.svg)
                  no-repeat center;
              }
            }
          }

          &.col-name .cell {
            word-break: break-all;
            word-wrap: break-word;
            display: -webkit-box;
            overflow: hidden;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
            line-clamp: 1;
          }
        }
        .el-table__row:first-child,
        .el-table__row--level-0 {
          .el-table__cell {
            .cell {
              font-weight: 500;
            }
          }
        }
      }
    }
  }
}
</style>
