import { cloneDeep, isNil, round } from "lodash-es";
import BigNumber from "bignumber.js";
import { thousandths } from "@/utils/formatNumber";
import { CommonColumn, NewQuantileColumns } from "../constant.js";
import { check_blob_error } from "@/utils/storeFun";
import { downloadFileWithName } from "@/utils/downloadFile";
import $message from "@/components/SmartIsland";
import { formatSelect } from "@/pages/Fast/ForecastDemand/formatData";

export const getQuantileLabel = (level) => {
  if (level <= 0.5) return "Narrow";
  if (level < 0.9) return "Mid";
  return "Wide";
};

export const getDefaultQuantileLevel = (level = "", config) => {
  if (level) return level;
  if (!Array.isArray(config) || config.length === 0) return "";
  if (config.length === 3) return config[1]?.quantile_level ?? "";
  return config[3]?.quantile_level ?? "";
};

export const getQuantileListLabel = (row, item, index) => {
  const quantileObj = row.forecast_quantile
    ?.find(
      (it) =>
        it.fiscal_week_name === item.fiscalWeek &&
        it.selected_quantile_level === row[item.prop]
    )
    ?.quantile_config?.find(
      (it) => it.quantile_level === row[item.prop]
    )?.label;
  if (quantileObj && row[item.prop]) {
    return `${quantileObj} (${row[item.prop] * 100}%)`;
  }
  return (index > 0 && "-") || "";
};

export const formatValue = (val) => {
  if (isNil(val)) {
    return "0";
  } else {
    return thousandths(round(val));
  }
};

export const formatTableData = (data, forecast_weeks, subLob) => {
  let quantileNumArr = forecast_weeks.map((it) => {
    return {
      fiscal_week_name: it,
      [`${it}+ml_forecast`]: 0,
      [`${it}+minRangeNum`]: 0,
      [`${it}+maxRangeNum`]: 0,
    };
  });
  let arr = cloneDeep(data);
  arr.forEach((it) => {
    it.forecast_quantile.forEach((item) => {
      item.quantile_config.forEach((i) => {
        i.label = getQuantileLabel(i.quantile_level);
      });
      it[item.fiscal_week_name + "+ml_forecast"] = item.ml_forecast;
      it[item.fiscal_week_name + "+quantile_config"] = item.quantile_config.map(
        (it) => {
          return {
            value: it.quantile_level,
            label: `${getQuantileLabel(it.quantile_level)} (${
              it.quantile_level * 100
            }%)`,
            min: it.quantile_min,
            minPercent: !item.ml_forecast
              ? "-"
              : BigNumber(it.quantile_min ?? 0)
                  .minus(BigNumber(item.ml_forecast ?? 0))
                  .div(BigNumber(item.ml_forecast ?? 0))
                  .times(100)
                  .toFixed(0),
            max: it.quantile_max,
            maxPercent: !item.ml_forecast
              ? "-"
              : BigNumber(it.quantile_max ?? 0)
                  .minus(BigNumber(item.ml_forecast ?? 0))
                  .div(BigNumber(item.ml_forecast ?? 0))
                  .times(100)
                  .toFixed(0),
            quantile_level: it.quantile_level,
          };
        }
      );
      it[item.fiscal_week_name + "+selected_quantile_level"] =
        item.selected_quantile_level;
      it[item.fiscal_week_name + "+range"] = `${formatValue(
        it[item.fiscal_week_name + "+quantile_config"]?.find(
          (i) =>
            i.quantile_level ===
            it[item.fiscal_week_name + "+selected_quantile_level"]
        )?.min
      )} <span>(${
        it[item.fiscal_week_name + "+quantile_config"]?.find(
          (i) =>
            i.quantile_level ===
            it[item.fiscal_week_name + "+selected_quantile_level"]
        )?.minPercent === "-"
          ? "-"
          : `${
              it[item.fiscal_week_name + "+quantile_config"]?.find(
                (i) =>
                  i.quantile_level ===
                  it[item.fiscal_week_name + "+selected_quantile_level"]
              )?.minPercent
            }%`
      })</span> ~ ${formatValue(
        it[item.fiscal_week_name + "+quantile_config"]?.find(
          (i) =>
            i.quantile_level ===
            it[item.fiscal_week_name + "+selected_quantile_level"]
        )?.max
      )} <span>(${
        it[item.fiscal_week_name + "+quantile_config"]?.find(
          (i) =>
            i.quantile_level ===
            it[item.fiscal_week_name + "+selected_quantile_level"]
        )?.maxPercent === "-"
          ? "-"
          : `+${
              it[item.fiscal_week_name + "+quantile_config"]?.find(
                (i) =>
                  i.quantile_level ===
                  it[item.fiscal_week_name + "+selected_quantile_level"]
              )?.maxPercent
            }%`
      })</span>`;
      let current = quantileNumArr.find(
        (it) => it.fiscal_week_name === item.fiscal_week_name
      );
      let current_quantile_config =
        it[item.fiscal_week_name + "+quantile_config"];
      current[`${item.fiscal_week_name}+ml_forecast`] += item.ml_forecast;
      current[`${item.fiscal_week_name}+minRangeNum`] +=
        current_quantile_config?.find(
          (i) =>
            i.quantile_level ===
            it[item.fiscal_week_name + "+selected_quantile_level"]
        )?.min;
      current[`${item.fiscal_week_name}+maxRangeNum`] +=
        current_quantile_config?.find(
          (i) =>
            i.quantile_level ===
            it[item.fiscal_week_name + "+selected_quantile_level"]
        )?.max;
    });
  });
  if (arr.length) {
    arr.unshift({
      forecast_quantile: quantileNumArr,
      rtm: "All",
      sub_lob: subLob,
      sub_rtm: "All",
    });
    quantileNumArr.forEach((it) => {
      let ml_forecast = it[`${it.fiscal_week_name}+ml_forecast`] ?? 0;
      arr[0][`${it.fiscal_week_name}+ml_forecast`] = ml_forecast;
      if (ml_forecast === 0) {
        it[`${it.fiscal_week_name}+range`] =
          "-<span>(-%)</span> ~ -<span>(-%)</span>";
      } else {
        arr[0][`${it.fiscal_week_name}+range`] = `${formatValue(
          it[`${it.fiscal_week_name}+minRangeNum`]
        )} <span>(${BigNumber(it[`${it.fiscal_week_name}+minRangeNum`] ?? 0)
          .minus(BigNumber(ml_forecast ?? 0))
          .div(BigNumber(ml_forecast ?? 0))
          .times(100)
          .toFixed(0)}%)</span> ~ ${formatValue(
          it[`${it.fiscal_week_name}+maxRangeNum`]
        )} <span>(+${BigNumber(it[`${it.fiscal_week_name}+maxRangeNum`] ?? 0)
          .minus(BigNumber(ml_forecast ?? 0))
          .div(BigNumber(ml_forecast ?? 0))
          .times(100)
          .toFixed(0)}%)</span>`;
      }
    });
  }
  return arr;
};

export const restoreTableData = (oTableData, data, subLob) => {
  let obj = cloneDeep(oTableData);
  let nArray = obj[subLob];
  data.forEach((it) => {
    let current = nArray.find((i) => i.index === it.index);
    current?.forecast_quantile.forEach((item) => {
      item.selected_quantile_level =
        it[item.fiscal_week_name + "+selected_quantile_level"];
    });
  });
  return obj;
};

export const formatColumn = (data, isOld = true) => {
  let columns = [];
  data?.forEach((it, index) => {
    columns.push(
      ...[
        {
          label: `CW${index > 0 ? "+" + index : ""} ML Fcst._${it.substr(4)}`,
          prop: `${it}+ml_forecast`,
          width: 160,
          definedSlot: "value",
          headerSlot: "headerMl",
          fiscalWeek: `${it}`,
        },
        {
          label: "Quantile Level",
          prop: `${it}+selected_quantile_level`,
          width: 170,
          definedSlot: "quantileLevel",
          fiscalWeek: `${it}`,
        },
        {
          label: "Quantile Range",
          prop: `${it}+range`,
          width: 300,
          definedSlot: "range",
          headerSlot: "headerRange",
          fiscalWeek: `${it}`,
          "class-name": index < data.length - 1 ? "col-border" : "",
        },
      ]
    );
  });
  const commonColumn = isOld ? CommonColumn : NewQuantileColumns;
  return [...commonColumn, ...columns];
};

export const formatList = (list) => {
  const data = cloneDeep(list);
  let levelOption = [];
  Object.keys(data).forEach((it) => {
    data[it] = data[it]?.reduce((acc, current, index) => {
      current.index = index;
      current.hideRtm = !!acc.find((it) => it.rtm === current.rtm);
      current.forecast_quantile.forEach((item) => {
        item.quantile_config = item.quantile_config.sort((a, b) =>
          Number(a.quantile_level - b.quantile_level)
        );
        if (index === 0) {
          levelOption = item.quantile_config.map((it) => {
            return {
              value: it.quantile_level,
              label: `${getQuantileLabel(it.quantile_level)} (${
                it.quantile_level * 100
              }%)`,
            };
          });
        }
        //设置默认值为mid
        item.selected_quantile_level = getDefaultQuantileLevel(
          item.selected_quantile_level,
          item.quantile_config
        );
      });
      acc.push(current);
      return acc;
    }, []);
  });
  return {
    data,
    levelOption,
  };
};

export const downloadFile = (res, fiscal_week_name) => {
  check_blob_error(
    res,
    (msg) => {
      $message.error(msg);
    },
    () => {
      const contentDisposition = res.headers["content-disposition"];
      let matches = contentDisposition.match(
        /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/
      );
      let filename =
        matches && matches[1]
          ? matches[1].replace(/['"]/g, "")
          : `Sold-to ML Forecast ${fiscal_week_name}.xlsx`;
      downloadFileWithName(res.data, filename);
    }
  );
};

export const formatSoldToOptions = (data = []) => {
  return data.map((item) => {
    return {
      ...item,
      label: `${item?.sold_to_id} ${item?.sold_to_name}`,
      value: item?.sold_to_id,
    };
  });
};

export const formatSubLobOptions = (data = []) => {
  return data.map((subLob) => {
    const { sub_lob = "", nand = [], color = [] } = subLob;
    return {
      label: sub_lob,
      value: sub_lob,
      nand: formatSelect(nand),
      color: formatSelect(color),
    };
  });
};

export const formatNewList = (list) => {
  const data = cloneDeep(list);
  let levelOption = [];
  Object.keys(data).forEach((it, index) => {
    data[it] = data[it]?.map((current, cindex) => {
      current.id = `${current.rtm}|${current.sub_rtm}|${current.sold_to_id}`;
      current.quantile.forEach((item) => {
        item.config = item.config.sort(
          (a, b) => Number(a.level) - Number(b.level)
        );
        item.config.forEach((i) => {
          i.label = getQuantileLabel(i.level);
        });
        if (index === 0 && cindex === 0) {
          levelOption = item.config.map((it) => {
            return {
              value: it.level,
              label: `${getQuantileLabel(it.level)} (${it.level * 100}%)`,
            };
          });
        }
        //设置默认值为mid
        item.selected = getDefaultQuantileLevel(item.selected, item.config);
      });
      return current;
    });
  });

  return {
    data,
    levelOption,
  };
};

const childMpn = "mpns";
const childSoldTo = "children";

export function formatHierarchicalData(data = [], forecast_weeks = []) {
  const hierarchy = buildHierarchicalStructure(data);

  const tree = hierarchy.map((node) =>
    processNodeForTable(node, forecast_weeks)
  );

  const allSummary = createAllSummary(tree, forecast_weeks);

  return tree.length ? [allSummary, ...tree] : [];
}


function createAllSummary(tree, forecast_weeks) {
  if (!tree.length) return null;

  // 聚合所有一级节点的quantile数据
  const allQuantiles = tree.map((node) => node.quantile).filter(Boolean);
  const aggregatedQuantile = aggregateQuantiles(allQuantiles);

  // 创建All汇总节点
  const allSummary = {
    id: "all",
    name: "All",
    rtm: "All",
    quantile: aggregatedQuantile,
    [childSoldTo]: [], // All节点没有子节点
  };

  // 为All节点添加表格格式数据
  return processNodeForTable(allSummary, forecast_weeks);
}

// 构建层级结构
function buildHierarchicalStructure(data = []) {
  const hierarchy = new Map();

  data.forEach((item) => {
    const { rtm, sub_rtm, sold_to_id, mpn_id, short_name, ...rest } = item;
    const path = [rtm, sub_rtm, sold_to_id];

    let current = hierarchy;
    let fullPath = "";

    const maxIndex = path.length - 1;
    path.forEach((key, index) => {
      const lastChildKey = index === maxIndex ? childMpn : childSoldTo;
      fullPath += `${fullPath ? "|" : ""}${key}`;

      if (!current.has(key)) {
        current.set(key, {
          id: fullPath,
          name: index === maxIndex ? short_name : key,
          [lastChildKey]: index === maxIndex ? [] : new Map(),
          ...getNodeFields(index, {
            rtm,
            sub_rtm,
            sold_to_id,
            short_name,
            ...rest,
          }),
        });
      }
      current = current.get(key)[lastChildKey];
    });

    // 添加mpn层数据
    current.push({
      rtm,
      sub_rtm,
      sold_to_id,
      short_name,
      mpn_id,
      ...rest,
    });
  });

  // 递归构建数组结构并聚合quantile
  return buildArrayStructure(hierarchy);
}

// 为表格处理节点数据
function processNodeForTable(node, forecast_weeks) {
  const processedNode = { ...node };

  // 处理quantile数据，转换为表格格式
  if (node.quantile?.length) {
    node.quantile.forEach((weekData) => {
      const week = weekData.week;
      const selected = weekData?.selected
        ? weekData?.selected
        : weekData?.default;
      processedNode[`${week}+ml_forecast`] = weekData.forecast;
      processedNode[`${week}+selected_quantile_level`] = selected;

      // 格式化quantile配置 - 使用BigNumber避免精度问题
      processedNode[`${week}+quantile_config`] = weekData.config.map((cfg) => ({
        value: cfg.level,
        label: `${getQuantileLabel(cfg.level)} (${cfg.level * 100}%)`,
        min: cfg.min,
        minPercent: !weekData.forecast
          ? "-"
          : BigNumber(cfg.min ?? 0)
              .minus(BigNumber(weekData.forecast ?? 0))
              .div(BigNumber(weekData.forecast ?? 0))
              .times(100)
              .toFixed(0),
        max: cfg.max,
        maxPercent: !weekData.forecast
          ? "-"
          : BigNumber(cfg.max ?? 0)
              .minus(BigNumber(weekData.forecast ?? 0))
              .div(BigNumber(weekData.forecast ?? 0))
              .times(100)
              .toFixed(0),
        quantile_level: cfg.level,
      }));

      // 生成range显示 当selected为mixed时，使用聚合的mixedRange数据
      const selectedConfig =
        selected === "Mixed" && weekData.mixedRange
          ? {
              min: weekData.mixedRange.min,
              max: weekData.mixedRange.max,
              minPercent: !weekData.forecast
                ? "-"
                : BigNumber(weekData.mixedRange.min ?? 0)
                    .minus(BigNumber(weekData.forecast ?? 0))
                    .div(BigNumber(weekData.forecast ?? 0))
                    .times(100)
                    .toFixed(0),
              maxPercent: !weekData.forecast
                ? "-"
                : BigNumber(weekData.mixedRange.max ?? 0)
                    .minus(BigNumber(weekData.forecast ?? 0))
                    .div(BigNumber(weekData.forecast ?? 0))
                    .times(100)
                    .toFixed(0),
            }
          : processedNode[`${week}+quantile_config`]?.find(
              (cfg) => cfg.quantile_level === selected
            );

      processedNode[`${week}+range`] = formatRangeDisplay(selectedConfig);
    });
  }

  // 递归处理子节点
  if (node[childSoldTo]?.length) {
    processedNode[childSoldTo] = node[childSoldTo].map((child) =>
      processNodeForTable(child, forecast_weeks)
    );
  }

  return processedNode;
}

// 格式化范围显示
function formatRangeDisplay(config) {
  if (!config) return "-";

  return `${formatValue(config.min)} <span>(${
    config.minPercent === "-" ? "-" : `${config.minPercent}%`
  })</span> ~ ${formatValue(config.max)} <span>(${
    config.maxPercent === "-" ? "-" : `+${config.maxPercent}%`
  })</span>`;
}

// 获取节点的字段
function getNodeFields(level, data) {
  const { rtm, sub_rtm, sold_to_id, short_name, ...rest } = data;
  switch (level) {
    case 0:
      return { rtm };
    case 1:
      return { rtm, sub_rtm };
    case 2:
      return { rtm, sub_rtm, sold_to_id, short_name, ...rest };
    default:
      return {};
  }
}

// 递归构建数组结构
function buildArrayStructure(mapStructure) {
  return Array.from(mapStructure.values()).map((node) => {
    if (Array.isArray(node?.[childMpn])) {
      // 叶子节点层级，直接聚合quantile
      node.quantile = aggregateQuantiles(
        node[childMpn].map((child) => child.quantile)
      );
    } else {
      // 中间层级，递归处理子节点
      node[childSoldTo] = buildArrayStructure(node[childSoldTo]);
      node.quantile = aggregateQuantiles(
        node[childSoldTo].map((child) => child.quantile)
      );
    }
    return node;
  });
}

// 聚合多个quantile数组
function aggregateQuantiles(quantileArrays) {
  if (!quantileArrays?.length) return [];

  const weeks = quantileArrays[0]?.map((item) => item.week) || [];

  return weeks.map((week) => {
    let forecast = new BigNumber(0);
    const levelMap = new Map();
    const selectedSet = new Set();

    // 用于计算mixed情况下的实际range聚合
    let mixedRangeMin = new BigNumber(0);
    let mixedRangeMax = new BigNumber(0);

    quantileArrays.forEach((arr) => {
      const weekItem = arr.find((item) => item.week === week);
      if (!weekItem) return;

      // 使用BigNumber进行精确的数值累加
      forecast = forecast.plus(BigNumber(weekItem.forecast ?? 0));
      const childSelected = weekItem?.selected
        ? weekItem?.selected
        : weekItem?.default;
      selectedSet.add(childSelected);

      // 聚合所有level的config
      weekItem.config.forEach(({ level, min, max }) => {
        const existing = levelMap.get(level);
        if (existing) {
          existing.min = existing.min.plus(BigNumber(min ?? 0));
          existing.max = existing.max.plus(BigNumber(max ?? 0));
        } else {
          levelMap.set(level, {
            level,
            min: new BigNumber(min ?? 0),
            max: new BigNumber(max ?? 0),
          });
        }
      });

      // 计算子节点实际选中的range，用于mixed情况
      if (childSelected === "Mixed" && weekItem.mixedRange) {
        // 如果子节点已经是mixed，使用其mixedRange数据
        mixedRangeMin = mixedRangeMin.plus(
          BigNumber(weekItem.mixedRange.min ?? 0)
        );
        mixedRangeMax = mixedRangeMax.plus(
          BigNumber(weekItem.mixedRange.max ?? 0)
        );
      } else {
        // 如果子节点不是mixed，从config中查找对应的配置
        const childSelectedConfig = weekItem.config.find(
          (cfg) => cfg.level === childSelected
        );
        if (childSelectedConfig) {
          mixedRangeMin = mixedRangeMin.plus(
            BigNumber(childSelectedConfig.min ?? 0)
          );
          mixedRangeMax = mixedRangeMax.plus(
            BigNumber(childSelectedConfig.max ?? 0)
          );
        }
      }
    });

    const selected =
      selectedSet.size === 1 ? selectedSet.values().next().value : "Mixed";
    const result = {
      week,
      forecast: forecast.toNumber(),
      config: Array.from(levelMap.values()).map((cfg) => ({
        level: cfg.level,
        min: cfg.min.toNumber(),
        max: cfg.max.toNumber(),
      })),
      selected,
    };

    // 如果是mixed，添加混合range信息
    if (selected === "Mixed") {
      result.mixedRange = {
        min: mixedRangeMin.toNumber(),
        max: mixedRangeMax.toNumber(),
      };
    }

    return result;
  });
}

export const getNewQuantileListLabel = (row, item, index) => {
  if (index === 0) return "";
  const options = row[`${item.fiscalWeek}+quantile_config`];
  const selected = row[`${item.fiscalWeek}+selected_quantile_level`];
  const selectedOption = options.find((it) => it.value === selected);
  if (selectedOption) {
    return `${selectedOption.label}`;
  }
  return row[`${item.fiscalWeek}+selected_quantile_level`];
};

// 更新节点的 quantile level 和 range
export function updateNodeQuantileLevel(node, week, newLevel) {
  if (!node) return;

  // 更新当前节点的 selected level
  node[`${week}+selected_quantile_level`] = newLevel;

  // 重新计算 range
  if (newLevel === "Mixed" && node.quantile) {
    // 如果是 mixed，使用 mixedRange 数据
    const weekData = node.quantile.find((item) => item.week === week);
    if (weekData && weekData.mixedRange) {
      const selectedConfig = {
        min: weekData.mixedRange.min,
        max: weekData.mixedRange.max,
        minPercent: !weekData.forecast
          ? "-"
          : BigNumber(weekData.mixedRange.min ?? 0)
              .minus(BigNumber(weekData.forecast ?? 0))
              .div(BigNumber(weekData.forecast ?? 0))
              .times(100)
              .toFixed(0),
        maxPercent: !weekData.forecast
          ? "-"
          : BigNumber(weekData.mixedRange.max ?? 0)
              .minus(BigNumber(weekData.forecast ?? 0))
              .div(BigNumber(weekData.forecast ?? 0))
              .times(100)
              .toFixed(0),
      };
      node[`${week}+range`] = formatRangeDisplay(selectedConfig);
    }
  } else {
    // 如果不是 mixed，从 quantile_config 中查找
    const selectedConfig = node[`${week}+quantile_config`]?.find(
      (cfg) => cfg.quantile_level === newLevel
    );
    node[`${week}+range`] = formatRangeDisplay(selectedConfig);
  }
}

// 递归更新所有子节点的 quantile level
export function updateChildrenQuantileLevel(node, week, newLevel) {
  if (!node) return;

  // 更新当前节点
  updateNodeQuantileLevel(node, week, newLevel);

  // 递归更新子节点
  if (node[childSoldTo]?.length) {
    node[childSoldTo].forEach((child) => {
      updateChildrenQuantileLevel(child, week, newLevel);
    });
  }
}

// 检查所有子节点的 quantile level 是否一致
export function checkChildrenQuantileLevel(node, week) {
  if (!node[childSoldTo]?.length) {
    return node[`${week}+selected_quantile_level`];
  }

  const childLevels = node[childSoldTo].map(
    (child) => child[`${week}+selected_quantile_level`]
  );

  const uniqueLevels = [...new Set(childLevels)];
  return uniqueLevels.length === 1 ? uniqueLevels[0] : "Mixed";
}

// 重新聚合父节点的 range（当子节点的 level 改变时）
export function recalculateParentRange(node, week) {
  if (!node[childSoldTo]?.length) return;

  const currentLevel = node[`${week}+selected_quantile_level`];

  if (currentLevel === "Mixed") {
    // 聚合子节点的实际选中 range
    let totalMin = new BigNumber(0);
    let totalMax = new BigNumber(0);
    let totalForecast = new BigNumber(0);

    node[childSoldTo].forEach((child) => {
      const childLevel = child[`${week}+selected_quantile_level`];
      const childForecast = child[`${week}+ml_forecast`] || 0;
      totalForecast = totalForecast.plus(BigNumber(childForecast));

      if (childLevel === "Mixed") {
        // 如果子节点也是 mixed，使用其 mixedRange
        const childQuantile = child.quantile?.find(
          (item) => item.week === week
        );
        if (childQuantile && childQuantile.mixedRange) {
          totalMin = totalMin.plus(
            BigNumber(childQuantile.mixedRange.min ?? 0)
          );
          totalMax = totalMax.plus(
            BigNumber(childQuantile.mixedRange.max ?? 0)
          );
        }
      } else {
        // 如果子节点不是 mixed，从其 config 中查找
        const childConfig = child[`${week}+quantile_config`]?.find(
          (cfg) => cfg.quantile_level === childLevel
        );
        if (childConfig) {
          totalMin = totalMin.plus(BigNumber(childConfig.min ?? 0));
          totalMax = totalMax.plus(BigNumber(childConfig.max ?? 0));
        }
      }
    });

    // 更新父节点的 range
    const forecast = totalForecast.toNumber();
    const selectedConfig = {
      min: totalMin.toNumber(),
      max: totalMax.toNumber(),
      minPercent: !forecast
        ? "-"
        : totalMin
            .minus(BigNumber(forecast))
            .div(BigNumber(forecast))
            .times(100)
            .toFixed(0),
      maxPercent: !forecast
        ? "-"
        : totalMax
            .minus(BigNumber(forecast))
            .div(BigNumber(forecast))
            .times(100)
            .toFixed(0),
    };

    node[`${week}+range`] = formatRangeDisplay(selectedConfig);

    // 更新 quantile 数据中的 mixedRange
    if (node.quantile) {
      const weekData = node.quantile.find((item) => item.week === week);
      if (weekData) {
        weekData.mixedRange = {
          min: totalMin.toNumber(),
          max: totalMax.toNumber(),
        };
      }
    }
  }
}

// 更新汇总行（第一行）的 range
export function updateSummaryRowRange(tableData, week) {
  if (!tableData || tableData.length === 0) return;

  const summaryRow = tableData[0]; // 第一行是汇总行
  if (!summaryRow) return;

  // 聚合所有一级节点的实际选中 range
  let totalMin = new BigNumber(0);
  let totalMax = new BigNumber(0);
  let totalForecast = new BigNumber(0);

  // 跳过第一行（汇总行），从第二行开始聚合
  tableData.slice(1).forEach((node) => {
    const nodeLevel = node[`${week}+selected_quantile_level`];
    const nodeForecast = node[`${week}+ml_forecast`] || 0;
    totalForecast = totalForecast.plus(BigNumber(nodeForecast));

    if (nodeLevel === "Mixed") {
      // 如果节点是 mixed，使用其 mixedRange
      const nodeQuantile = node.quantile?.find((item) => item.week === week);
      if (nodeQuantile && nodeQuantile.mixedRange) {
        totalMin = totalMin.plus(BigNumber(nodeQuantile.mixedRange.min ?? 0));
        totalMax = totalMax.plus(BigNumber(nodeQuantile.mixedRange.max ?? 0));
      }
    } else {
      // 如果节点不是 mixed，从其 config 中查找
      const nodeConfig = node[`${week}+quantile_config`]?.find(
        (cfg) => cfg.quantile_level === nodeLevel
      );
      if (nodeConfig) {
        totalMin = totalMin.plus(BigNumber(nodeConfig.min ?? 0));
        totalMax = totalMax.plus(BigNumber(nodeConfig.max ?? 0));
      }
    }
  });

  // 更新汇总行的 range
  const forecast = totalForecast.toNumber();
  const selectedConfig = {
    min: totalMin.toNumber(),
    max: totalMax.toNumber(),
    minPercent: !forecast
      ? "-"
      : totalMin
          .minus(BigNumber(forecast))
          .div(BigNumber(forecast))
          .times(100)
          .toFixed(0),
    maxPercent: !forecast
      ? "-"
      : totalMax
          .minus(BigNumber(forecast))
          .div(BigNumber(forecast))
          .times(100)
          .toFixed(0),
  };

  summaryRow[`${week}+range`] = formatRangeDisplay(selectedConfig);

  // 更新汇总行的 quantile 数据中的 mixedRange
  if (summaryRow.quantile) {
    const weekData = summaryRow.quantile.find((item) => item.week === week);
    if (weekData) {
      weekData.mixedRange = {
        min: totalMin.toNumber(),
        max: totalMax.toNumber(),
      };
    }
  }
}

// 递归更新父节点的 quantile level 和 range
export function updateParentQuantileLevel(tableData, targetNode, week) {
  // 找到目标节点的父节点
  function findParentNode(nodes, target, parent = null) {
    for (const node of nodes) {
      if (node === target) {
        return parent;
      }
      if (node[childSoldTo]?.length) {
        const found = findParentNode(node[childSoldTo], target, node);
        if (found !== null) return found;
      }
    }
    return null;
  }

  const parentNode = findParentNode(tableData, targetNode);
  if (!parentNode) {
    // 如果没有找到父节点，说明已经到达顶级，更新汇总行
    updateSummaryRowRange(tableData, week);
    return;
  }

  // 检查父节点的所有子节点的 level 是否一致
  const newParentLevel = checkChildrenQuantileLevel(parentNode, week);

  // 更新父节点的 level
  updateNodeQuantileLevel(parentNode, week, newParentLevel);

  // 重新计算父节点的 range
  recalculateParentRange(parentNode, week);

  // 递归更新更上级的父节点
  updateParentQuantileLevel(tableData, parentNode, week);
}
