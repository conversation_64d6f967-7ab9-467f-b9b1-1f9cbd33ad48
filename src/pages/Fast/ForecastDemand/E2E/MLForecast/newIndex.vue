<template>
  <div>
    <HeaderView
      :currentLob="currentLob"
      :lastPublishTime="lastPublishTime"
      :isEdit="isEdit"
      :publishing="publishing"
      :disabled="disabled"
      @publish="publishService"
    />
    <Card title="ML Quantile Forecast Setting" class="data-container">
      <template #options>
        <TitleOption :params="params" :fiscal_week_name="fiscal_week_name" />
      </template>
      <template>
        <div class="filterWrapper">
          <FilterView
            :subLob.sync="subLob"
            :nand.sync="nandValue"
            :color.sync="colorValue"
            :searchValue.sync="searchValue"
            :subLobOptions="subLobOptions"
            :soldToOptions="soldToOptions"
            :isEdit="isEdit"
            @change="filterQuantileList"
          />
          <BtnView
            :isEdit="isEdit"
            :disabled="disabled"
            @reset="getQuantileList"
            @save="handleSave"
            @cancel="handleCancel"
            @edit="isEdit = true"
            @batch="showBatch = true"
          />
        </div>
        <NewTable
          :tableColumn="tableColumn"
          :tableData="tableData"
          :loading="loading"
          :isEdit="isEdit"
          @levelChange="levelChange"
        />
      </template>
    </Card>
    <BatchDrawer
      v-if="showBatch"
      :visible.sync="showBatch"
      :fiscal-week-list="weekList"
      :rtm-list="rtmList"
      :sub-rtm-list="subRtmList"
      :sub-lob-list="subLobOptions"
      :level-list="levelOption"
      @closeDrawer="handleBatch"
    />
  </div>
</template>
<script>
import Card from "@/pages/Fast/components/Card.vue";
import {
  quantileNewMenu,
  quantileNewList,
  quantilePublish,
  quantileUpdate,
  quantileBatchUpdate,
} from "@/services/ForecastDemand.js";
import BatchDrawer from "./components/BatchDrawer.vue";
import { formatSelect } from "@/pages/Fast/ForecastDemand/formatData";
import HeaderView from "./components/HeaderView.vue";
import BtnView from "./components/BtnView.vue";
import NewTable from "./components/NewTable.vue";
import {
  formatNewTableData,
  formatValue,
  restoreTableData,
  formatColumn,
  formatNewList,
  formatSoldToOptions,
  formatSubLobOptions,
  formatHierarchicalData,
} from "./format";
import FilterView from "./components/FilterView.vue";
import TitleOption from "./components/TitleOption.vue";
import { mock } from "./mock";

export default {
  components: {
    Card,
    BatchDrawer,
    HeaderView,
    BtnView,
    NewTable,
    TitleOption,
    FilterView,
  },
  props: {
    currentLob: {
      type: String,
      default: "",
    },
    fiscal_week_name: {
      type: String,
      default: "",
    },
    disabled: {
      type: Boolean,
      default: true,
    },
    params: {
      type: Object,
      default: () => {},
    },
    isOldView: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loading: false,
      isEdit: false,
      publishing: false,
      showBatch: false,
      pageLoading: null,

      tableColumn: [],
      oTableData: null,
      tableData: [],

      fiscal_week: "",
      forecast_weeks: [],
      lastPublishTime: "",

      subLob: "",
      nandValue: "All",
      colorValue: "All",
      subLobOptions: [],
      searchValue: "",
      rtmList: [],
      subRtmList: [],
      levelOption: [],
      soldToOptions: [],
    };
  },
  computed: {
    weekList() {
      return this.forecast_weeks.map((it, index) => ({
        label: `CW${index > 0 ? "+" + index : ""}(${it.substr(4)})`,
        value: it,
      }));
    },
  },
  created() {
    // this.pageLoading = this.$fastloading();
    this.getQuantileMenu();
  },
  methods: {
    formatValue,
    getQuantileMenu(noChangeSubLob) {
      quantileNewMenu(this.params)
        .then(({ code = 0, data = {} }) => {
          if (code === 0) {
            this.fiscal_week = data?.fiscal_week;
            this.forecast_weeks = data?.forecast_weeks;
            this.lastPublishTime = data?.latest_publish_time;
            this.soldToOptions = formatSoldToOptions(
              data?.rtm_subrtm_soldto ?? []
            );
            this.subLobOptions = formatSubLobOptions(
              data?.sublob_nand_color ?? []
            );
            if (!noChangeSubLob) {
              this.subLob = this.subLobOptions?.[0]?.value ?? "";
            }
            this.rtmList = data?.rtms?.map((it) => ({
              ...it,
              value: it.rtm,
              label: it.rtm,
            }));
            this.subRtmList = data?.rtms?.reduce((pre, item) => {
              pre.push(...formatSelect(item.sub_rtms));
              return pre;
            }, []);
            this.tableColumn = formatColumn(
              data?.forecast_weeks ?? [],
              this.isOldView
            );
            this.getQuantileList();
          }
        })
        .catch((error) => {
          console.warn(error);
        });
    },
    async getQuantileList() {
      // this.$nextTick(() => {
      //   const { data: list, levelOption } = formatNewList(mock);
      //   this.levelOption = levelOption;
      //   this.oTableData = list;
      //   this.tableData = formatHierarchicalData(
      //     this.oTableData?.[this.subLob] ?? [],
      //     this.forecast_weeks,
      //     this.subLob
      //   );
      // });
      // this.loading = true;
      try {
        const { code = 0, data = {} } = await quantileNewList({
          ...this.params,
          sub_lob: this.subLob,
        });
        if (code === 0) {
          const { data: list, levelOption } = formatNewList(data);
          this.levelOption = levelOption;
          this.oTableData = list;
          this.tableData = formatHierarchicalData(
            this.oTableData?.[this.subLob] ?? [],
            this.forecast_weeks,
            this.subLob
          );
        }
      } catch (error) {
        console.warn(error);
      } finally {
        // this.loading = false;
        // this.pageLoading?.close();
      }
    },
    levelChange(row) {
      this.tableData.splice(0, 1, row);
    },
    filterQuantileList(val) {
      // this.loading = true;
      this.tableData = formatNewTableData(
        this.oTableData?.[val] ?? [],
        this.forecast_weeks,
        this.subLob
      );
      // this.loading = false;
    },
    publishService() {
      this.publishing = true;
      this.loading = true;
      quantilePublish({
        ...this.params,
        fiscal_week: this.fiscal_week,
        forecast_weeks: this.forecast_weeks,
        data: this.oTableData,
      })
        .then(({ code = 0 }) => {
          if (code === 0) {
            this.$message.success({
              message:
                "Result Published, You can update quantile range again before 12:00.",
              duration: 5000,
            });
            this.getQuantileMenu(true);
          }
        })
        .catch((error) => {
          console.warn(error);
        })
        .finally(() => {
          this.loading = false;
          this.publishing = false;
        });
    },
    async handleCancel() {
      await this.getQuantileList();
      this.isEdit = false;
    },
    handleSave() {
      this.saveLoading = this.$fastloading({ text: "Saving" });
      quantileUpdate({
        ...this.params,
        fiscal_week: this.fiscal_week,
        forecast_weeks: this.forecast_weeks,
        data: restoreTableData(this.oTableData, this.tableData, this.subLob),
      })
        .then(({ code = 0 }) => {
          if (code === 0) {
            this.isEdit = false;
            this.$message.success({
              message: "Result Saved.",
              duration: 5000,
            });
            this.getQuantileList();
          }
        })
        .catch((error) => {
          console.warn(error);
        })
        .finally(() => {
          this.saveLoading.close();
        });
    },
    handleBatch(params) {
      this.showBatch = false;
      this.saveLoading = this.$fastloading({ text: "Saving" });
      quantileBatchUpdate({
        fiscal_week: this.fiscal_week,
        forecast_weeks: this.forecast_weeks,
        ...this.params,
        ...params,
      })
        .then(({ code = 0 }) => {
          if (code === 0) {
            this.$message.success({
              message: "Result Saved.",
              duration: 5000,
            });
            this.getQuantileList();
          }
        })
        .catch((error) => {
          console.warn(error);
        })
        .finally(() => {
          this.saveLoading.close();
        });
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .card.data-container .content {
  display: block !important;
}
</style>
