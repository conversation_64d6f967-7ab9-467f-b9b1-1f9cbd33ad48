<template>
  <div>
    <HeaderView
      :currentLob="currentLob"
      :lastPublishTime="lastPublishTime"
      :isEdit="isEdit"
      :publishing="publishing"
      :disabled="disabled"
      :isOldView="isOldView"
      @publish="publishService"
    />
    <Card title="ML Quantile Forecast Setting" class="data-container">
      <template #options>
        <TitleOption
          :params="params"
          :fiscal_week_name="fiscal_week_name"
          :isEdit="isEdit"
          :isOldView="isOldView"
        />
      </template>
      <template>
        <div class="filterWrapper">
          <FilterView
            :subLob.sync="subLob"
            :nand.sync="nandValue"
            :color.sync="colorValue"
            :searchValue.sync="searchValue"
            :subLobOptions="subLobOptions"
            :soldToOptions="soldToOptions"
            :isEdit="isEdit"
            @getData="getQuantileList"
            @change="filterQuantileList"
          />
          <BtnView
            :isEdit="isEdit"
            :disabled="disabled"
            @reset="setTableData"
            @save="handleSave"
            @cancel="handleCancel"
            @edit="isEdit = true"
            @batch="showBatch = true"
          />
        </div>
        <NewTable
          :tableColumn="tableColumn"
          :tableData="tableData"
          :loading="loading"
          :isEdit="isEdit"
          @levelChange="levelChange"
        />
      </template>
    </Card>
    <BatchDrawer
      v-if="showBatch"
      :visible.sync="showBatch"
      :fiscal-week-list="weekList"
      :rtm-list="rtmList"
      :sub-rtm-list="subRtmList"
      :sub-lob-list="subLobOptions"
      :level-list="levelOption"
      @closeDrawer="handleBatch"
    />
  </div>
</template>
<script>
import Card from "@/pages/Fast/components/Card.vue";
import {
  quantileNewMenu,
  quantileNewList,
  quantileNewUpdate,
  quantileNewBatchUpdate,
  quantileNewPublishChecker,
  quantileNewPublish,
} from "@/services/ForecastDemand.js";
import BatchDrawer from "./components/BatchDrawer.vue";
import { formatSelect } from "@/pages/Fast/ForecastDemand/formatData";
import HeaderView from "./components/HeaderView.vue";
import BtnView from "./components/BtnView.vue";
import NewTable from "./components/NewTable.vue";
import {
  formatValue,
  formatColumn,
  formatNewList,
  formatSoldToOptions,
  formatSubLobOptions,
  formatHierarchicalData,
  extractFourthLevelData,
} from "./format";
import FilterView from "./components/FilterView.vue";
import TitleOption from "./components/TitleOption.vue";
import { PublishNewMessage } from "../constant";

export default {
  components: {
    Card,
    BatchDrawer,
    HeaderView,
    BtnView,
    NewTable,
    TitleOption,
    FilterView,
  },
  props: {
    currentLob: {
      type: String,
      default: "",
    },
    fiscal_week_name: {
      type: String,
      default: "",
    },
    disabled: {
      type: Boolean,
      default: true,
    },
    params: {
      type: Object,
      default: () => {},
    },
    isOldView: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loading: false,
      isEdit: false,
      publishing: false,
      showBatch: false,
      pageLoading: null,

      tableColumn: [],
      originData: null,
      filterData: null,
      tableData: [],

      fiscal_week: "",
      forecast_weeks: [],
      lastPublishTime: "",

      subLob: "",
      nandValue: "All",
      colorValue: "All",
      subLobOptions: [],
      searchValue: "",
      rtmList: [],
      subRtmList: [],
      levelOption: [],
      soldToOptions: [],
    };
  },
  computed: {
    weekList() {
      return this.forecast_weeks.map((it, index) => ({
        label: `CW${index > 0 ? "+" + index : ""}(${it.substr(4)})`,
        value: it,
      }));
    },
  },
  created() {
    this.pageLoading = this.$fastloading();
    this.getQuantileMenu();
  },
  methods: {
    formatValue,
    getQuantileMenu(noChangeSubLob) {
      quantileNewMenu(this.params)
        .then(({ code = 0, data = {} }) => {
          if (code === 0) {
            this.fiscal_week = data?.fiscal_week;
            this.forecast_weeks = data?.forecast_weeks;
            this.lastPublishTime = data?.latest_publish_time;
            this.soldToOptions = formatSoldToOptions(
              data?.rtm_subrtm_soldto ?? []
            );
            this.subLobOptions = formatSubLobOptions(
              data?.sublob_nand_color ?? []
            );
            if (!noChangeSubLob) {
              this.subLob = this.subLobOptions?.[0]?.value ?? "";
            }
            this.rtmList = data?.rtms?.map((it) => ({
              ...it,
              value: it.rtm,
              label: it.rtm,
            }));
            this.subRtmList = data?.rtms?.reduce((pre, item) => {
              pre.push(...formatSelect(item.sub_rtms));
              return pre;
            }, []);
            this.tableColumn = formatColumn(
              data?.forecast_weeks ?? [],
              this.isOldView
            );
          }
        })
        .catch((error) => {
          console.warn(error);
        });
    },
    getQuantileList() {
      this.loading = true;
      quantileNewList({
        ...this.params,
        sub_lob: this.subLob,
      })
        .then(({ code = 0, data = {} }) => {
          if (code === 0) {
            const { data: origin, levelOption } = formatNewList(
              data?.[this.subLob] ?? []
            );
            this.levelOption = levelOption;
            this.originData = origin;
            this.filterQuantileList({
              nand: this.nandValue,
              color: this.colorValue,
              search_value: this.searchValue,
            });
          }
        })
        .catch((error) => {
          console.warn(error);
        })
        .finally(() => {
          this.loading = false;
          this.pageLoading?.close();
        });
    },
    setTableData() {
      this.tableData = formatHierarchicalData(
        this.filterData,
        this.forecast_weeks,
        this.searchValue // 传递搜索值，用于控制层级显示
      );
    },
    levelChange(data) {
      this.tableData = data;
    },
    filterQuantileList(val) {
      this.filterData = this.originData.filter((item) => {
        return (
          (val?.nand === "All" || item.nand === val?.nand) &&
          (val?.color === "All" || item.color === val?.color) &&
          (val?.search_value === "" ||
            String(item.sold_to_id) === String(val?.search_value))
        );
      });
      this.setTableData();
    },
    publishService() {
      this.publishing = true;
      this.loading = true;
      quantileNewPublishChecker(this.params)
        .then(({ code = 0, data = {} }) => {
          if (code === 0) {
            if (!PublishNewMessage?.[data?.is_all_adjusted]) {
              this.handlePublish();
              return;
            }
            this.$message.warning({
              message: PublishNewMessage[data?.is_all_adjusted],
              showConfirmBtn: true,
              showCancelBtn: true,
              onConfirm: (close) => {
                this.handlePublish();
                close();
              },
              onCancel: (close) => {
                this.loading = false;
                this.publishing = false;
                close();
              },
            });
          }
        })
        .catch((error) => {
          console.warn(error);
          this.loading = false;
          this.publishing = false;
        });
    },
    handlePublish() {
      quantileNewPublish({
        ...this.params,
        fiscal_week: this.fiscal_week,
      })
        .then(({ code = 0, data = "" }) => {
          if (code === 0) {
            this.$message.success({
              message:
                "Result Published, You can update quantile range again before 12:00.",
              duration: 5000,
            });
            this.lastPublishTime = data;
          }
        })
        .catch((error) => {
          console.warn(error);
        })
        .finally(() => {
          this.loading = false;
          this.publishing = false;
        });
    },
    handleSave() {
      this.saveLoading = this.$fastloading({ text: "Saving" });
      quantileNewUpdate({
        ...this.params,
        fiscal_week: this.fiscal_week,
        forecast_weeks: this.forecast_weeks,
        data: extractFourthLevelData(this.tableData, this.subLob),
      })
        .then(({ code = 0 }) => {
          if (code === 0) {
            this.isEdit = false;
            this.$message.success({
              message: "Result Saved.",
              duration: 5000,
            });
            this.getQuantileList();
          }
        })
        .catch((error) => {
          console.warn(error);
        })
        .finally(() => {
          this.saveLoading.close();
        });
    },
    handleCancel() {
      this.setTableData();
      this.isEdit = false;
    },
    handleBatch(params) {
      this.showBatch = false;
      this.saveLoading = this.$fastloading({ text: "Saving" });
      quantileNewBatchUpdate({
        fiscal_week: this.fiscal_week,
        forecast_weeks: this.forecast_weeks,
        ...this.params,
        ...params,
      })
        .then(({ code = 0 }) => {
          if (code === 0) {
            this.$message.success({
              message: "Result Saved.",
              duration: 5000,
            });
            this.getQuantileList();
          }
        })
        .catch((error) => {
          console.warn(error);
        })
        .finally(() => {
          this.saveLoading.close();
        });
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .card.data-container .content {
  display: block !important;
}
</style>
