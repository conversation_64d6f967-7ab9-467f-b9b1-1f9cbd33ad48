export const LobList = [
  { value: "iPhone", label: "iPhone", disabled: false },
  { value: "iPad", label: "iPad", disabled: true },
  { value: "CPU", label: "CPU", disabled: true },
  { value: "Watch", label: "Watch", disabled: true },
];
export const CPFStepList = [
  {
    step: 1,
    title: "ML Quantile Forecast",
    param: "FAST_E2E_QUANTILE",
    status: "",
  },
  {
    step: 2,
    title: "Sell-in Demand",
    param: "FAST_E2E_FEEDBACK_SETTING",
    status: "",
  },
  {
    step: 3,
    title: "Demand Feedback Review",
    param: "FAST_E2E_FEEDBACK_REVIEW",
    status: "",
  },
];
export const RTMStepList = [
  {
    step: 1,
    title: "Forecast Preparation",
    param: "FAST_E2E_FORECAST_PREPARATION",
    status: "",
  },
  {
    step: 2,
    title: "Demand Feedback Review",
    param: "FAST_E2E_DEMAND_FEEDBACK_REVIEW",
    status: "",
  },
  {
    step: 3,
    title: "Sold-to Demand Finalization",
    status: "",
    param: "FAST_E2E_DEMAND_FINALIZATION",
  },
];
export const CommonColumn = [
  { label: "RTM", prop: "rtm", width: 110, fixed: "left", definedSlot: "rtm" },
  { label: "Sub-RTM", prop: "sub_rtm", width: 150, fixed: "left" },
];
export const E2ETips = [
  {
    type: "text",
    info: "To manually adjust final demand results, please download data from overview module as template and re-upload the updated spreadsheet to system.",
  },
  {
    type: "text",
    info: "By data uploaded operation, the system will refresh final demand results and roll-over the last version. The CW+2 results is automatically re-calculated when CW+1 results is manually adjusted.",
  },
  {
    type: "html",
    info: 'With the template, you may only need to update "<span style="font-weight:600;color:#1C1C1E">Base Demand</span>" and "<span style="font-weight:600;color:#1C1C1E">Delta Demand</span>" results by your judgement and any other template fields shall not be modified. Please make sure that total quantities (Base + Delta Demand) remain unchanged on MPN level.',
  },
  {
    type: "text",
    info: "Please always remember to publish new demand result in the “Overview” module once you finish manual adjustment. Without publish confirmation, the data result would not be shared to RTM planners.",
  },
];
export const CpfDemandMPNColumn = [
  { label: "Nand", prop: "nand", width: 100, fixed: "left" },
  { label: "Color", prop: "color", width: 180, fixed: "left" },
  {
    label: "Demand 1.0+AI ML",
    prop: "suggestion_demand",
    width: 117,
    headerSlot: "header",
    definedSlot: "value",
  },
  {
    label: "Demand 2.0+Reseller",
    prop: "feedback_demand",
    width: 117,
    headerSlot: "header",
    definedSlot: "value",
  },
  {
    label: "Demand 3.0+Approved",
    prop: "finalized_demand",
    width: 117,
    headerSlot: "header",
    definedSlot: "value",
  },
  {
    label: "Delta+2.0 - 1.0",
    prop: "suggestion_delta",
    width: 106,
    headerSlot: "header",
    definedSlot: "value",
  },
  {
    label: "Delta+3.0 - 2.0",
    prop: "feedback_delta",
    width: 106,
    headerSlot: "header",
    definedSlot: "value",
  },
  {
    label: "Demand 2.0 WOI+vs. Reseller",
    prop: "feedback_woi",
    width: 160,
    headerSlot: "header",
    definedSlot: "woi",
  },
  {
    label: "Demand 3.0 WOI+vs. Reseller",
    prop: "finalized_woi",
    width: 160,
    headerSlot: "header",
    definedSlot: "woi",
  },
  {
    label: "Demand 2.0 WOI+vs. DFA",
    prop: "feedback_woi_dfa",
    width: 160,
    headerSlot: "header",
    definedSlot: "woi",
  },
  {
    label: "Demand 3.0 WOI+vs. DFA",
    prop: "finalized_woi_dfa",
    width: 160,
    headerSlot: "header",
    definedSlot: "woi",
  },
  {
    label: "Reseller Runrate",
    prop: "feedback_runrate",
    width: 148,
    definedSlot: "value",
  },
  {
    label: "DFA Runrate",
    prop: "dfa_runrate",
    width: 116,
    definedSlot: "value",
  },
];
export const CpfDemandChannelColumn = [
  { label: "RTM", prop: "rtm", width: 100, fixed: "left" },
  { label: "Sub-RTM", prop: "sub_rtm", width: 180, fixed: "left" },
  {
    label: "Demand 1.0+AI ML",
    prop: "suggestion_demand",
    width: 117,
    headerSlot: "header",
    definedSlot: "value",
  },
  {
    label: "Demand 2.0+Reseller",
    prop: "feedback_demand",
    width: 117,
    headerSlot: "header",
    definedSlot: "value",
  },
  {
    label: "Demand 3.0+Approved",
    prop: "finalized_demand",
    width: 117,
    headerSlot: "header",
    definedSlot: "value",
  },
  {
    label: "Delta+2.0 - 1.0",
    prop: "suggestion_delta",
    width: 106,
    headerSlot: "header",
    definedSlot: "value",
  },
  {
    label: "Delta+3.0 - 2.0",
    prop: "feedback_delta",
    width: 106,
    headerSlot: "header",
    definedSlot: "value",
  },
  {
    label: "Demand 2.0 WOI+vs. Reseller",
    prop: "feedback_woi",
    width: 160,
    headerSlot: "header",
    definedSlot: "woi",
  },
  {
    label: "Demand 3.0 WOI+vs. Reseller",
    prop: "finalized_woi",
    width: 160,
    headerSlot: "header",
    definedSlot: "woi",
  },
  {
    label: "Reseller Runrate",
    prop: "feedback_runrate",
    width: 148,
    definedSlot: "value",
  },
];

export const RTMFeedbackReviewColumn = [
  { label: "Sub-RTM", prop: "sub_rtm", width: 139 },
  { label: "Reseller Type", prop: "reseller_type", width: 130 },
  {
    label: "Reseller",
    prop: "disti",
    width: 340,
    definedSlot: "disti",
  },
  { label: "Latest Feedback Time", prop: "feedback_time", width: 220 },
  {
    label: "My Approval Status",
    prop: "status",
    // width: 320,
    definedSlot: "status",
  },
  { label: "Operation", prop: "sub_rtm", width: 120, definedSlot: "operation" },
];

export const RtmDemandMPNColumn = [
  { label: "Nand", prop: "nand", width: 150, fixed: "left" },
  { label: "Color", prop: "color", fixed: "left" },
  {
    label: "Demand 1.0+AI ML",
    prop: "suggestion_demand",
    headerSlot: "header",
    definedSlot: "value",
  },
  {
    label: "Demand 2.0+Reseller",
    prop: "feedback_demand",
    headerSlot: "header",
    definedSlot: "value",
  },
  {
    label: "Demand 3.0+Approved",
    prop: "finalized_demand",
    headerSlot: "header",
    definedSlot: "value",
  },
  {
    label: "Demand 3.0 WOI+vs. Reseller",
    prop: "finalized_woi",
    headerSlot: "header",
    definedSlot: "woi",
  },
  {
    label: "Reseller Runrate",
    prop: "feedback_runrate",
    headerSlot: "headerRunrate",
    definedSlot: "value",
  },
];

export const AccountInterfaceSelectList = [
  // 0303新增
  {
    label: "4227887 北京金麦克发科技有限公司",
    value: "4227887",
    rtm: "Mono",
    sub_rtm: "Lifestyle",
  },
  {
    label: "3356989 河南轩明好彩商贸有限公司",
    value: "3356989",
    rtm: "Mono",
    sub_rtm: "Mono",
  },
  {
    label: "3355652 浙江奇易网络技术有限公司",
    value: "3355652",
    rtm: "Mono",
    sub_rtm: "Mono",
  },
  {
    label: "3483969 广西百动数码科技有限公司",
    value: "3483969",
    rtm: "Mono",
    sub_rtm: "Mono",
  },
  {
    label: "3382045 成都海佳科技有限公司",
    value: "3382045",
    rtm: "Mono",
    sub_rtm: "Mono",
  },
  {
    label: "3489164 成都明讯天辰贸易有限公司",
    value: "3489164",
    rtm: "Mono",
    sub_rtm: "Mono",
  },
  {
    label: "3415577 福建北辰智慧科技有限公司",
    value: "3415577",
    rtm: "Mono",
    sub_rtm: "Mono",
  },
  {
    label: "4252675 福建昕佳悦信息科技有限公司",
    value: "4252675",
    rtm: "Mono",
    sub_rtm: "Mono",
  },
  {
    label: "3389757 贵州鑫高维通讯设备有限公司",
    value: "3389757",
    rtm: "Mono",
    sub_rtm: "Mono",
  },
  {
    label: "3412629 海南新燕海佳通信有限公司",
    value: "3412629",
    rtm: "Mono",
    sub_rtm: "Mono",
  },
  {
    label: "1294441 河北劲草商贸有限公司",
    value: "1294441",
    rtm: "Mono",
    sub_rtm: "Mono",
  },
  {
    label: "3434506 湖北澳易通商贸有限公司",
    value: "3434506",
    rtm: "Mono",
    sub_rtm: "Mono",
  },
  {
    label: "3426057 无锡纵驰传媒有限公司",
    value: "3426057",
    rtm: "Mono",
    sub_rtm: "Mono",
  },
  {
    label: "3495268 西藏星宇通讯科技股份有限公司",
    value: "3495268",
    rtm: "Mono",
    sub_rtm: "Mono",
  },
  {
    label: "3435975 浙江迈风网络科技有限公司",
    value: "3435975",
    rtm: "Mono",
    sub_rtm: "Mono",
  },
  {
    label: "3405858 南京隆宝数码科技有限公司",
    value: "3405858",
    rtm: "Mono",
    sub_rtm: "Mono",
  },
  {
    label: "4291673 北京中科星迈科技有限公司",
    value: "4291673",
    rtm: "Mono",
    sub_rtm: "Mono",
  },
  // 5个
  {
    label: "3569041 北京鑫耀瑞达科技有限公司",
    value: "3569041",
    rtm: "Mono",
    sub_rtm: "Lifestyle",
  },
  {
    label: "649296 英迈电子商贸（上海）有限公司",
    value: "649296",
    rtm: "Mono",
    sub_rtm: "Lifestyle",
  },
  {
    label: "1035217 上海英迈物流有限公司",
    value: "1035217",
    rtm: "Mono",
    sub_rtm: "Lifestyle",
  },
  {
    label: "3582295 上海晟达元信息技术有限公司",
    value: "3582295",
    rtm: "Mono",
    sub_rtm: "Lifestyle",
  },
  {
    label: "3449268 海南聚时科技有限公司",
    value: "3449268",
    rtm: "Mono",
    sub_rtm: "Lifestyle",
  },
];

export const SpecialRtmList = [
  // "Multi",
  // "Online",
  // "Carrier",
  "Education",
  "Enterprise",
];

export const NewQuantileColumns = [
  {
    label: "",
    prop: "name",
    width: 240,
    fixed: "left",
    definedSlot: "rtm",
    "class-name": "col-name",
  },
];
